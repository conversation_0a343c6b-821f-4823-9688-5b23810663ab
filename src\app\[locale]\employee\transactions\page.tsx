
"use client"; 

import React, { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CreditCard, PlusCircle, ListChecks, Printer, DollarSign, UserCircleIcon, BookText } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from '@/components/ui/badge';
import { useScopedI18n } from '@/lib/i18n/client'; 
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-toast';
import type { Debt, DebtItem } from '@/types';
import { format } from 'date-fns';
import { Textarea } from '@/components/ui/textarea';


const DEBTS_STORAGE_KEY = 'marketSyncOwnerDebts';

const getStoredDebts = (ownerId?: string): Debt[] => {
  if (typeof window === 'undefined') return [];
  const stored = localStorage.getItem(DEBTS_STORAGE_KEY);
  let allDebts: Debt[] = [];
  if (stored) {
    allDebts = JSON.parse(stored);
  }
  return ownerId ? allDebts.filter(d => d.ownerId === ownerId) : allDebts;
};

const saveStoredDebts = (debts: Debt[]) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(DEBTS_STORAGE_KEY, JSON.stringify(debts));
  }
};


// Simplified mock transaction type for this page's display
interface DisplayTransaction {
    id: string;
    orderId: string;
    customer?: string; // Optional for cash sales, required for credit
    amount: number;
    methodOrType: string; // "Card", "Cash", "Credit Sale"
    timestamp: string;
    status: string; // "Completed", "Pending" (for cash), or "Unpaid" (for debt)
    notes?: string; // Added notes
}


const mockRecentTransactions: DisplayTransaction[] = [
    { id: 'TRN001', orderId: 'ORD5821', customer: 'Alice B.', amount: 45.90, methodOrType: 'Card', timestamp: '10:32 AM', status: 'Completed', notes: 'Payment for order 5821' },
    { id: 'TRN002', orderId: 'ORD5822', customer: 'Robert C.', amount: 12.50, methodOrType: 'Cash', timestamp: '10:45 AM', status: 'Completed', notes: 'Quick cash sale' },
    { id: 'TRN003', orderId: 'ORD5823', customer: 'Eve D.', amount: 88.00, methodOrType: 'Card', timestamp: '11:15 AM', status: 'Pending', notes: 'Awaiting confirmation' },
];

export default function EmployeeTransactionsPage() {
  const t = useScopedI18n('employeeTransactions'); 
  const tCommon = useScopedI18n('common');
  const tDebts = useScopedI18n('debtsManagement');
  const { user: authUser } = useAuth();
  const { toast } = useToast();

  const [transactionType, setTransactionType] = useState<'cash' | 'credit'>('cash');
  const [orderId, setOrderId] = useState('');
  const [customerName, setCustomerName] = useState(''); // For credit sales
  const [amount, setAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState(''); // For cash sales
  const [notes, setNotes] = useState(''); 

  const [recentTransactions, setRecentTransactions] = useState<DisplayTransaction[]>(mockRecentTransactions);
  const [lastRecordedTransaction, setLastRecordedTransaction] = useState<DisplayTransaction | null>(null);

  const canGrantCredit = authUser?.permissions?.canGrantCredit || false;


  const handleRecordTransaction = () => {
    if (!authUser || !authUser.createdById) {
        toast({ title: tCommon('error'), description: t('errorOwnerNotAssociated'), variant: "destructive" });
        return;
    }

    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
        toast({ title: tCommon('error'), description: t('errorInvalidAmount'), variant: "destructive" });
        return;
    }
    
    let finalOrderId = orderId.trim();
    if (!finalOrderId) {
      finalOrderId = `${transactionType.toUpperCase()}-${Date.now().toString().slice(-6)}`;
    }


    if (transactionType === 'cash') {
        if (!paymentMethod) {
            toast({ title: tCommon('error'), description: t('errorPaymentMethodRequired'), variant: "destructive" });
            return;
        }
        
        const newCashTransaction: DisplayTransaction = {
            id: `CTRN${Date.now().toString().slice(-6)}`,
            orderId: finalOrderId,
            customer: customerName.trim() || undefined,
            amount: numericAmount,
            methodOrType: paymentMethod === 'Cash' ? t('paymentMethodCash') : paymentMethod === 'AlKuraimiBank' ? t('paymentMethodAlKuraimiBank') : t('paymentMethodEWallet'),
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            status: 'Completed',
            notes: notes.trim() || undefined,
        };
        setRecentTransactions(prev => [newCashTransaction, ...prev]);
        setLastRecordedTransaction(newCashTransaction);
        toast({ title: tCommon('success'), description: t('successCashSaleRecorded') });

    } else if (transactionType === 'credit') {
        if (!canGrantCredit) {
            toast({ title: tCommon('error'), description: t('errorPermissionDeniedCredit'), variant: "destructive" });
            return;
        }
        if (!customerName.trim()) {
            toast({ title: tCommon('error'), description: t('errorCustomerNameRequired'), variant: "destructive" });
            return;
        }

        const allDebts = getStoredDebts();
        const debtId = `DEBT${Date.now().toString().slice(-6)}`;
        
        const newDebtItem: DebtItem = {
          productId: `CREDIT_SALE_${finalOrderId}`,
          productName: t('creditSaleItemName', {orderId: finalOrderId}),
          quantity: 1,
          price: numericAmount,
        };

        const newDebt: Debt = {
            id: debtId,
            partyName: customerName.trim(),
            type: 'debt',
            amount: numericAmount,
            date: format(new Date(), 'yyyy-MM-dd'),
            status: 'unpaid',
            notes: notes.trim() || t('defaultDebtNote', {orderId: finalOrderId}),
            items: [newDebtItem],
            ownerId: authUser.createdById,
        };
        
        saveStoredDebts([...allDebts, newDebt]);
        
        const newCreditDisplayTransaction: DisplayTransaction = {
            id: newDebt.id,
            orderId: finalOrderId,
            customer: customerName.trim(),
            amount: numericAmount,
            methodOrType: t('creditSaleTransactionType'),
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            status: 'Unpaid',
            notes: newDebt.notes,
        };
        setRecentTransactions(prev => [newCreditDisplayTransaction, ...prev]);
        setLastRecordedTransaction(newCreditDisplayTransaction);
        toast({ title: tCommon('success'), description: t('successCreditSaleRecorded', { customerName: customerName.trim() }) });
    }

    // Reset form fields
    setOrderId('');
    setCustomerName('');
    setAmount('');
    setPaymentMethod('');
    setNotes('');
  };

  const handlePrintReceipt = (transactionToPrint: DisplayTransaction | null) => {
    if (!transactionToPrint) {
      toast({ title: tCommon('error'), description: t('noTransactionToPrint'), variant: "destructive" });
      return;
    }

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const direction = tCommon('language') === 'Arabic' ? 'rtl' : 'ltr';
      let receiptHTML = `<html><head><title>${t('receiptTitle')}</title><style>`;
      receiptHTML += `
        body { font-family: Arial, sans-serif; margin: 20px; color: #333; direction: ${direction}; font-size: 12px; }
        .receipt-box { max-width: 300px; margin: auto; padding: 15px; border: 1px solid #ccc; }
        .receipt-header { text-align: center; margin-bottom: 10px; }
        .receipt-header h1 { margin: 0; font-size: 1.2em; color: hsl(var(--primary)); }
        .receipt-header p { margin: 1px 0; font-size: 0.8em; }
        .detail-item { display: flex; justify-content: space-between; margin-bottom: 4px; }
        .detail-item strong { font-weight: bold; }
        .total-section { text-align: ${direction === 'rtl' ? 'left' : 'right'}; margin-top: 10px; font-size: 1.1em; font-weight: bold; border-top: 1px dashed #ccc; padding-top: 5px; }
        .notes-section { margin-top: 10px; font-size: 0.9em; border-top: 1px dashed #ccc; padding-top: 5px;}
        .footer { text-align: center; margin-top: 15px; font-size: 0.8em; color: #777; }
      `;
      receiptHTML += `</style></head><body><div class="receipt-box">`;
      receiptHTML += `<div class="receipt-header"><h1>${authUser?.name || tDebts('storeNamePlaceholder')}</h1><p>${t('receiptTitle')}</p></div>`; 
      
      receiptHTML += `<div class="detail-item"><span>${t('transactionIdLabel')}:</span><strong>${transactionToPrint.id}</strong></div>`;
      receiptHTML += `<div class="detail-item"><span>${t('orderIdLabel')}:</span><strong>${transactionToPrint.orderId}</strong></div>`;
      receiptHTML += `<div class="detail-item"><span>${tDebts('date')}:</span><strong>${format(new Date(), "PP")} ${transactionToPrint.timestamp}</strong></div>`;
      if (transactionToPrint.customer) {
        receiptHTML += `<div class="detail-item"><span>${t('tableHeaderCustomer')}:</span><strong>${transactionToPrint.customer}</strong></div>`;
      }
      receiptHTML += `<div class="detail-item"><span>${t('transactionTypeLabel')}:</span><strong>${transactionToPrint.methodOrType}</strong></div>`;
      
      if(transactionToPrint.notes){
        receiptHTML += `<div class="notes-section"><strong>${tDebts('notesOptional').replace(' (Optional)', '').replace('(اختياري)','')}:</strong><p>${transactionToPrint.notes}</p></div>`;
      }

      receiptHTML += `<div class="total-section"><span>${tDebts('totalAmount')}:</span><span>YER ${transactionToPrint.amount.toFixed(2)}</span></div>`;
      
      receiptHTML += `<div class="footer">${t('receiptFooterThanks')}</div>`;
      receiptHTML += `</div></body></html>`;
      printWindow.document.write(receiptHTML);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
    } else {
      toast({ title: tCommon('error'), description: tDebts('printWindowError'), variant: 'destructive' });
    }
  };


  return (
    <AuthenticatedLayout expectedRole="employee">
      <div className="space-y-8 p-1">
        {/* Enhanced Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="p-3 bg-primary/10 rounded-2xl">
              <CreditCard className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-foreground">{t('title')}</h1>
              <p className="text-lg text-muted-foreground mt-1">
                {t('description')}
              </p>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="flex gap-4">
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-xl">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">15,250</div>
              <div className="text-sm text-green-600 dark:text-green-400">مبيعات اليوم (ريال)</div>
            </div>
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">8</div>
              <div className="text-sm text-blue-600 dark:text-blue-400">معاملات اليوم</div>
            </div>
          </div>
        </div>
        
        <div className="grid lg:grid-cols-3 gap-8">
            {/* Enhanced Transaction Form */}
            <Card className="employee-card lg:col-span-1">
                <CardHeader className="employee-card-header">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                        <CreditCard className="h-6 w-6 text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <CardTitle className="text-xl">{t('newTransactionTitle')}</CardTitle>
                        <CardDescription className="text-base">
                        {t('newTransactionDescription')}
                        </CardDescription>
                      </div>
                    </div>
                </CardHeader>
                <CardContent className="employee-card-content space-y-6">
                    {/* Enhanced Transaction Type Selection */}
                    <div className="space-y-3">
                        <Label htmlFor="transactionType" className="text-base font-medium">{t('transactionTypeLabel')}</Label>
                        <Select
                            value={transactionType}
                            onValueChange={(value: 'cash' | 'credit') => {
                                if (value === 'credit' && !canGrantCredit) {
                                    toast({ title: tCommon('error'), description: t('errorPermissionDeniedCredit'), variant: "destructive" });
                                    return;
                                }
                                setTransactionType(value);
                                setLastRecordedTransaction(null);
                            }}
                        >
                            <SelectTrigger id="transactionType" className="employee-input h-12">
                                <SelectValue placeholder={t('transactionTypePlaceholder')} />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="cash" className="py-3">
                                  <div className="flex items-center gap-3">
                                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                                    <span>{t('cashSaleTransactionType')}</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="credit" disabled={!canGrantCredit} className="py-3">
                                  <div className="flex items-center gap-3">
                                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                                    <div>
                                      <span>{t('creditSaleTransactionType')}</span>
                                      {!canGrantCredit && <span className="text-xs text-muted-foreground block">({t('permissionDenied')})</span>}
                                    </div>
                                  </div>
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Enhanced Order ID Field */}
                    <div className="space-y-2">
                        <Label htmlFor="orderId" className="text-base font-medium">{t('orderIdLabel')}</Label>
                        <Input
                          id="orderId"
                          value={orderId}
                          onChange={(e) => setOrderId(e.target.value)}
                          placeholder={t('orderIdPlaceholderOptional')}
                          className="employee-input h-12"
                        />
                    </div>

                    {/* Enhanced Customer Name Field */}
                    {(transactionType === 'credit' || (transactionType === 'cash' && customerName.trim())) && (
                         <div className="space-y-2">
                            <Label htmlFor="customerName" className="text-base font-medium">
                              {transactionType === 'credit' ? t('customerNameLabel') : t('customerNameCashLabel')}
                            </Label>
                            <Input
                                id="customerName"
                                value={customerName}
                                onChange={(e) => setCustomerName(e.target.value)}
                                placeholder={transactionType === 'credit' ? t('customerNamePlaceholder') : t('customerNameCashPlaceholder')}
                                className="employee-input h-12"
                            />
                        </div>
                    )}

                    {/* Enhanced Amount Field */}
                     <div className="space-y-2">
                        <Label htmlFor="amount" className="text-base font-medium">{t('amountLabel')}</Label>
                        <div className="relative">
                            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground rtl:right-3 rtl:left-auto" />
                            <Input
                              id="amount"
                              type="number"
                              value={amount}
                              onChange={(e) => setAmount(e.target.value)}
                              placeholder="0.00"
                              className="employee-input h-12 pl-10 rtl:pr-10 rtl:pl-3 text-lg font-medium"
                            />
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground rtl:left-3 rtl:right-auto">
                              ريال
                            </div>
                        </div>
                    </div>

                    {/* Enhanced Payment Method Field */}
                    {transactionType === 'cash' && (
                        <div className="space-y-2">
                            <Label htmlFor="paymentMethod" className="text-base font-medium">{t('paymentMethodLabel')}</Label>
                            <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                                <SelectTrigger id="paymentMethod" className="employee-input h-12">
                                    <SelectValue placeholder={t('paymentMethodPlaceholder')} />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Cash" className="py-3">
                                      <div className="flex items-center gap-3">
                                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <span>{t('paymentMethodCash')}</span>
                                      </div>
                                    </SelectItem>
                                    <SelectItem value="AlKuraimiBank" className="py-3">
                                      <div className="flex items-center gap-3">
                                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                                        <span>{t('paymentMethodAlKuraimiBank')}</span>
                                      </div>
                                    </SelectItem>
                                    <SelectItem value="EWallet" className="py-3">
                                      <div className="flex items-center gap-3">
                                        <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                                        <span>{t('paymentMethodEWallet')}</span>
                                      </div>
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    )}

                    {/* Enhanced Notes Field */}
                    <div className="space-y-2">
                        <Label htmlFor="notes" className="text-base font-medium">{tDebts('notesOptional')}</Label>
                        <Textarea
                          id="notes"
                          value={notes}
                          onChange={(e) => setNotes(e.target.value)}
                          placeholder={t('notesPlaceholder')}
                          className="employee-input min-h-[80px] resize-none"
                        />
                    </div>

                    {/* Enhanced Action Buttons */}
                    <div className="space-y-3 pt-4">
                      <Button
                          onClick={handleRecordTransaction}
                          className="employee-button employee-button-primary w-full h-12 text-lg font-medium"
                          disabled={transactionType === 'credit' && !canGrantCredit}
                      >
                          {transactionType === 'cash' ? <PlusCircle className="mr-2 h-5 w-5 rtl:ml-2 rtl:mr-0" /> : <BookText className="mr-2 h-5 w-5 rtl:ml-2 rtl:mr-0" />}
                          {transactionType === 'cash' ? t('recordCashSaleButton') : t('recordCreditSaleButton')}
                      </Button>
                      <Button
                          variant="outline"
                          className="employee-button w-full h-12"
                          onClick={() => handlePrintReceipt(lastRecordedTransaction)}
                          disabled={!lastRecordedTransaction}
                        >
                          <Printer className="mr-2 h-5 w-5 rtl:ml-2 rtl:mr-0" /> {t('printReceiptButton')}
                      </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Enhanced Transactions Table */}
            <Card className="employee-card lg:col-span-2">
                <CardHeader className="employee-card-header">
                    <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                            <ListChecks className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <CardTitle className="text-xl">{t('recentTransactionsTitle')}</CardTitle>
                            <CardDescription className="text-base">
                            {t('recentTransactionsDescription')}
                            </CardDescription>
                          </div>
                        </div>
                        <Button variant="outline" size="sm" className="employee-button">
                            <ListChecks className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('viewFullLogButton')}
                        </Button>
                    </div>
                    <div className="relative mt-4">
                      <Search className="absolute right-3 rtl:right-auto rtl:left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder={t('searchTransactionsPlaceholder')}
                        className="employee-input pr-10 rtl:pr-3 rtl:pl-10"
                      />
                    </div>
                </CardHeader>
                <CardContent className="employee-card-content">
                    {recentTransactions.length === 0 ? (
                        <div className="text-center py-12">
                          <CreditCard className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                          <p className="text-muted-foreground text-lg">{t('noRecentTransactions')}</p>
                          <p className="text-sm text-muted-foreground mt-2">ابدأ بتسجيل أول معاملة لك</p>
                        </div>
                    ) : (
                    <div className="employee-table rounded-xl overflow-hidden">
                        <Table>
                            <TableHeader className="employee-table-header">
                                <TableRow>
                                <TableHead className="font-semibold">{t('tableHeaderOrderId')}</TableHead>
                                <TableHead className="font-semibold">{t('tableHeaderCustomer')}</TableHead>
                                <TableHead className="text-right rtl:text-left font-semibold">{t('tableHeaderAmount')}</TableHead>
                                <TableHead className="font-semibold">{t('tableHeaderMethodOrType')}</TableHead>
                                <TableHead className="font-semibold">{t('tableHeaderTime')}</TableHead>
                                <TableHead className="font-semibold">{t('tableHeaderStatus')}</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {recentTransactions.map((trn) => (
                                <TableRow key={trn.id} className="employee-table-row">
                                    <TableCell className="font-medium">
                                      <div className="flex items-center gap-2">
                                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                        <span className="text-primary font-semibold">{trn.orderId}</span>
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex items-center gap-2">
                                            <UserCircleIcon className="h-4 w-4 text-muted-foreground" />
                                            <span className="font-medium">{trn.customer || tCommon('N_A')}</span>
                                        </div>
                                    </TableCell>
                                    <TableCell className="text-right rtl:text-left">
                                      <div className="font-bold text-lg text-primary">
                                        {trn.amount.toFixed(2)} ريال
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                      <div className="flex items-center gap-2">
                                        <div className={`w-3 h-3 rounded-full ${
                                          trn.methodOrType === 'Cash' ? 'bg-green-500' :
                                          trn.methodOrType === 'Credit' ? 'bg-blue-500' : 'bg-purple-500'
                                        }`}></div>
                                        <span>{trn.methodOrType}</span>
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                      <div className="text-sm">
                                        <div className="font-medium">{trn.timestamp.split(' ')[0]}</div>
                                        <div className="text-muted-foreground">{trn.timestamp.split(' ')[1]}</div>
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                        <Badge
                                          variant={trn.status === 'Completed' ? 'default' : (trn.status === 'Unpaid' ? 'destructive' : 'secondary')}
                                          className="employee-badge"
                                        >
                                            {t(`status${trn.status.replace(/\s+/g, '')}` as any, undefined, trn.status)}
                                        </Badge>
                                    </TableCell>
                                </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                    )}
                </CardContent>
            </Card>
        </div>

      </div>
    </AuthenticatedLayout>
  );
}

