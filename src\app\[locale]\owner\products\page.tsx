
"use client";

import React, { useState, useEffect, useRef } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Image from 'next/image';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Package, PlusCircle, Edit3, Trash2, Filter, ArrowUpDown, Layers, Loader2, Sparkles } from 'lucide-react';
import type { Product } from '@/types';
import { Badge } from '@/components/ui/badge';
import { getMockProducts, addMockProduct, updateMockProduct, deleteMockProduct } from '@/lib/mock-product-data';
import { useToast } from '@/hooks/use-toast';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useScopedI18n } from '@/lib/i18n/client';
import { generateOrEnhanceProductImageAction } from '@/app/actions/ai-actions';
import type { EnhanceProductImageInput } from '@/ai/flows/enhance-product-image-flow';
import { useAuth } from '@/contexts/auth-context';


type ProductFormValues = Omit<Product, 'id' | 'imageUrl' | 'dataAiHint'> & {
  imageUrl?: string;
  dataAiHint?: string;
};


export default function OwnerProductsPage() {
  const { user } = useAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filterText, setFilterText] = useState('');
  const [isProductModalOpen, setIsProductModalOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [currentProductForm, setCurrentProductForm] = useState<Partial<Product>>({});
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { toast } = useToast();
  const t = useScopedI18n('ownerProductManagement'); 
  const tCommon = useScopedI18n('common');
  

  useEffect(() => {
    if (user) { // Ensure user is loaded before loading products
        loadProducts();
    } else {
        setIsLoading(false); // If no user, stop loading
    }
  }, [user]); 

  const loadProducts = () => {
    if (!user) return;
    setIsLoading(true);
    if (typeof window !== 'undefined') {
      const allProducts = getMockProducts();
      setProducts(allProducts.filter(p => p.ownerId === user.id));
    }
    setIsLoading(false);
  };

  const resolveStockStatus = (stock?: number): 'statusInStock' | 'statusLowStock' | 'statusOutOfStock' => {
    if (stock === undefined) return 'statusOutOfStock';
    if (stock === 0) return 'statusOutOfStock';
    if (stock < 10) return 'statusLowStock';
    return 'statusInStock';
  };

  const getStockBadgeVariant = (status: 'statusInStock' | 'statusLowStock' | 'statusOutOfStock') => {
    switch (status) {
      case 'statusInStock': return 'default';
      case 'statusLowStock': return 'secondary';
      case 'statusOutOfStock': return 'destructive';
      default: return 'outline';
    }
  };

  const handleAddOrEditProduct = () => {
    if (!user) {
        toast({ title: tCommon('error'), description: "User not authenticated.", variant: "destructive"});
        return;
    }
    if (!currentProductForm.name || !currentProductForm.category || currentProductForm.price === undefined) {
      toast({ title: tCommon('error'), description: t('validation_message_required_fields'), variant: "destructive"});
      return;
    }

    const productData: Product = {
        id: editingProduct ? editingProduct.id : `prod${Date.now()}`,
        name: currentProductForm.name,
        category: currentProductForm.category,
        price: Number(currentProductForm.price),
        stock: Number(currentProductForm.stock || 0),
        isOnline: currentProductForm.isOnline || false,
        imageUrl: currentProductForm.imageUrl || `https://picsum.photos/100/100`,
        dataAiHint: currentProductForm.dataAiHint || currentProductForm.category.toLowerCase(),
        minOrderQuantity: Number(currentProductForm.minOrderQuantity || 1),
        ownerId: user.id, 
    };

    if (editingProduct) {
      updateMockProduct(productData);
      toast({ title: t('toast_product_updated_title'), description: t('toast_product_updated_desc', { productName: productData.name }) });
    } else {
      addMockProduct(productData); 
      toast({ title: t('toast_product_added_title'), description: t('toast_product_added_desc', { productName: productData.name }) });
    }
    loadProducts(); 
    setIsProductModalOpen(false);
    setEditingProduct(null);
    setCurrentProductForm({});
    if (fileInputRef.current) {
        fileInputRef.current.value = ""; 
    }
  };
  
  const openEditModal = (product: Product) => {
    setEditingProduct(product);
    setCurrentProductForm({...product});
    setIsProductModalOpen(true);
  };

  const openAddModal = () => {
    setEditingProduct(null);
    setCurrentProductForm({ isOnline: false, stock: 0, price: 0.00, minOrderQuantity: 1 });
    setIsProductModalOpen(true);
  };

  const handleDeleteProduct = (productId: string) => {
    if (window.confirm(t('confirm_delete_product'))) { 
        deleteMockProduct(productId);
        loadProducts();
        toast({title: t('toast_product_deleted_title'), description: t('toast_product_deleted_desc'), variant: "destructive"});
    }
  };

  const handleFormInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const val = type === 'number' ? parseFloat(value) : value;
    setCurrentProductForm(prev => ({ ...prev, [name]: val }));
  };
   const handleFormSwitchChange = (checked: boolean) => {
    setCurrentProductForm(prev => ({ ...prev, isOnline: checked }));
  };

  const handleGenerateImageWithAI = async () => {
    if (!currentProductForm.name || !currentProductForm.category) {
        toast({ title: tCommon('error'), description: t('validation_message_name_category_for_ai'), variant: "destructive"});
        return;
    }
    setIsGeneratingImage(true);
    toast({ title: t('toast_generating_image_title'), description: t('toast_generating_image_desc')});

    let baseImageDataUri: string | undefined = undefined;
    if (fileInputRef.current?.files && fileInputRef.current.files[0]) {
        const file = fileInputRef.current.files[0];
        baseImageDataUri = await new Promise((resolve) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result as string);
            reader.readAsDataURL(file);
        });
    }


    try {
        const aiInput: EnhanceProductImageInput = {
            productName: currentProductForm.name,
            productCategory: currentProductForm.category,
            dataAiHint: currentProductForm.dataAiHint,
            baseImageDataUri: baseImageDataUri,
        };
        const result = await generateOrEnhanceProductImageAction(aiInput);
        if (result?.generatedImageDataUri) {
            setCurrentProductForm(prev => ({ ...prev, imageUrl: result.generatedImageDataUri }));
            toast({ title: tCommon('success'), description: t('toast_image_generated_success') });
        } else {
            throw new Error(t('toast_image_generated_error_desc'));
        }
    } catch (error) {
        toast({ title: tCommon('error'), description: (error as Error).message || t('toast_image_generated_error_desc'), variant: "destructive" });
    } finally {
        setIsGeneratingImage(false);
    }
  };


  const filteredProducts = products.filter(product =>
    (product.name.toLowerCase().includes(filterText.toLowerCase()) ||
    product.category.toLowerCase().includes(filterText.toLowerCase())) &&
    product.ownerId === user?.id 
  );

  if (isLoading && typeof window !== 'undefined' && (!localStorage.getItem('marketSyncMockProducts') || !user)) {
    return (
      <AuthenticatedLayout expectedRole="owner">
        <div className="flex flex-col justify-center items-center h-64 p-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">{tCommon('loading')}</p> 
        </div>
      </AuthenticatedLayout>
    );
  }
  if (!user) { // Added check for user after loading state
    return (
      <AuthenticatedLayout expectedRole="owner">
        <div className="flex flex-col justify-center items-center h-64 p-4">
          <p className="text-muted-foreground">{tCommon('error')}: User not authenticated.</p>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-8 p-1">
        {/* Enhanced Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
          <div className="flex items-center space-x-4">
            <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 text-white shadow-lg">
              <Package className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                {t('title')}
              </h1>
              <p className="text-lg text-muted-foreground mt-1">
                {t('description')}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Badge variant="outline" className="text-purple-600 border-purple-200 bg-purple-50">
              <Package className="h-3 w-3 mr-1" />
              {filteredProducts.length} منتج
            </Badge>
            <Button
              onClick={openAddModal}
              className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <PlusCircle className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
              {t('add_new_product_button')}
            </Button>
          </div>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>{t('product_catalog_title')}</CardTitle>
            <CardDescription>
              {t('product_catalog_description')}
            </CardDescription>
            <div className="flex flex-col md:flex-row gap-2 mt-4 items-center">
                <div className="relative flex-grow w-full md:w-auto">
                    <Filter className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground rtl:right-2.5 rtl:left-auto" />
                    <Input 
                        placeholder={t('filter_placeholder')} 
                        className="pl-8 w-full rtl:pr-8 rtl:pl-3"
                        value={filterText}
                        onChange={(e) => setFilterText(e.target.value)}
                    />
                </div>
                <Button variant="outline">
                    <Layers className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('manage_categories_button')}
                </Button>
                 <Button variant="outline">
                    <ArrowUpDown className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('sort_by_button')}
                </Button>
            </div>
          </CardHeader>
          <CardContent>
            {filteredProducts.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">{t('no_products_message')}</p>
            ) : (
            <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[80px] hidden sm:table-cell">{t('table_header_image')}</TableHead>
                      <TableHead>{t('table_header_name')}</TableHead>
                      <TableHead className="hidden md:table-cell">{t('table_header_category')}</TableHead>
                      <TableHead className="text-right rtl:text-left">{t('table_header_price')}</TableHead>
                      <TableHead className="text-center">{t('table_header_stock')}</TableHead>
                      <TableHead>{t('table_header_status')}</TableHead>
                      <TableHead className="text-center">{t('table_header_online')}</TableHead>
                      <TableHead className="text-right rtl:text-left">{tCommon('actions')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.map((product) => {
                      const stockStatusKey = resolveStockStatus(product.stock);
                      return (
                      <TableRow key={product.id}>
                        <TableCell className="hidden sm:table-cell">
                          <Image 
                            src={product.imageUrl || `https://picsum.photos/100/100`} 
                            alt={product.name} 
                            width={50} 
                            height={50} 
                            className="rounded-md object-cover"
                            data-ai-hint={product.dataAiHint === 'apple' ? 'تفاح فوجي' : 
                                          product.dataAiHint === 'bread' ? 'خبز حبوب' : 
                                          product.dataAiHint === 'eggs' ? 'كرتون بيض' : 
                                          product.dataAiHint === 'olive oil' ? 'زيت زيتون' : 
                                          product.dataAiHint === 'coffee beans' ? 'حبوب بن' :
                                          product.dataAiHint === 'cheese block' ? 'قالب جبن' :
                                          product.dataAiHint === 'pasta spaghetti' ? 'مكرونة اسباجتي' :
                                          product.dataAiHint === 'orange juice' ? 'عصير برتقال' :
                                          product.dataAiHint === 'toilet paper' ? 'ورق تواليت' :
                                          product.dataAiHint === 'dish soap' ? 'صابون اطباق' :
                                          product.dataAiHint || product.category.toLowerCase()}
                          />
                        </TableCell>
                        <TableCell className="font-medium">{product.name}</TableCell>
                        <TableCell className="hidden md:table-cell">{product.category}</TableCell>
                        <TableCell className="text-right rtl:text-left">YER {(product.price || 0).toFixed(2)}</TableCell>
                        <TableCell className="text-center">{product.stock}</TableCell>
                        <TableCell>
                            <Badge variant={getStockBadgeVariant(stockStatusKey) as any}>
                                {t(stockStatusKey)}
                            </Badge>
                        </TableCell>
                        <TableCell className="text-center">
                           <Badge variant={product.isOnline ? 'default' : 'outline'}>
                             {product.isOnline ? tCommon('yes') : tCommon('no')}
                           </Badge>
                        </TableCell>
                        <TableCell className="text-right rtl:text-left space-x-1 rtl:space-x-reverse">
                          <Button variant="ghost" size="icon" aria-label={tCommon('edit')} onClick={() => openEditModal(product)}>
                            <Edit3 className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="text-destructive hover:text-destructive" aria-label={tCommon('delete')} onClick={() => handleDeleteProduct(product.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    )})}
                  </TableBody>
                </Table>
            </div>
            )}
          </CardContent>
        </Card>
      </div>

       <Dialog open={isProductModalOpen} onOpenChange={setIsProductModalOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>{editingProduct ? t('dialog_edit_product_title') : t('dialog_add_product_title')}</DialogTitle>
            <DialogDescription>
              {editingProduct ? t('dialog_edit_product_desc') : t('dialog_add_product_desc')}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-2">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="productName" className="text-right rtl:text-left col-span-1">{t('form_label_name')}</Label>
              <Input id="productName" name="name" value={currentProductForm.name || ''} onChange={handleFormInputChange} className="col-span-3" placeholder={t('form_placeholder_name')} />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="productCategory" className="text-right rtl:text-left col-span-1">{t('form_label_category')}</Label>
              <Input id="productCategory" name="category" value={currentProductForm.category || ''} onChange={handleFormInputChange} className="col-span-3" placeholder={t('form_placeholder_category')} />
            </div>
             <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="productPrice" className="text-right rtl:text-left col-span-1">{t('form_label_price')}</Label>
              <Input id="productPrice" name="price" type="number" value={currentProductForm.price || ''} onChange={handleFormInputChange} className="col-span-3" placeholder="0.00" step="0.01"/>
            </div>
             <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="productStock" className="text-right rtl:text-left col-span-1">{t('form_label_stock')}</Label>
              <Input id="productStock" name="stock" type="number" value={currentProductForm.stock || ''} onChange={handleFormInputChange} className="col-span-3" placeholder="0"/>
            </div>

            <div className="col-span-4">
                <Label htmlFor="productImageUrlExisting" className="text-sm font-medium">{t('form_label_image_url')}</Label>
                <Input 
                    id="productImageUrlExisting" 
                    name="imageUrl" 
                    value={currentProductForm.imageUrl || ''} 
                    onChange={handleFormInputChange} 
                    className="mt-1" 
                    placeholder={t('form_placeholder_image_url')} 
                />
                 {currentProductForm.imageUrl && (
                    <div className="mt-2 relative w-24 h-24 border rounded-md overflow-hidden">
                        <Image src={currentProductForm.imageUrl} alt={t('form_alt_current_image')} fill style={{objectFit: 'cover'}} />
                    </div>
                )}
            </div>

            <div className="col-span-4 space-y-2 border-t pt-4 mt-4">
                <h3 className="text-md font-semibold">{t('product_image_generation_title')}</h3>
                <p className="text-xs text-muted-foreground">{t('product_image_generation_description')}</p>
                <div>
                    <Label htmlFor="aiImageUpload">{t('upload_for_ai_label')}</Label>
                    <Input id="aiImageUpload" type="file" accept="image/*" ref={fileInputRef} className="mt-1 file:text-primary file:font-medium file:mr-4 rtl:file:ml-4 rtl:file:mr-0"/>
                </div>
                 <div>
                    <Label htmlFor="productDataAiHint">{t('ai_hint_for_generation_label')}</Label>
                    <Input 
                        id="productDataAiHint" 
                        name="dataAiHint" 
                        value={currentProductForm.dataAiHint || ''} 
                        onChange={handleFormInputChange} 
                        className="mt-1" 
                        placeholder={t('ai_hint_for_generation_placeholder')}
                    />
                </div>
                <Button type="button" onClick={handleGenerateImageWithAI} disabled={isGeneratingImage} variant="outline" className="w-full">
                    {isGeneratingImage ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Sparkles className="mr-2 h-4 w-4" />}
                    {isGeneratingImage ? tCommon('loading') : t('generate_enhance_ai_button')}
                </Button>
            </div>


             <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="productMinOrder" className="text-right rtl:text-left col-span-1">{t('form_label_min_order')}</Label>
              <Input id="productMinOrder" name="minOrderQuantity" type="number" value={currentProductForm.minOrderQuantity || ''} onChange={handleFormInputChange} className="col-span-3" placeholder="1"/>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="productIsOnline" className="text-right rtl:text-left col-span-1">{t('form_label_online')}</Label>
              <div className="col-span-3">
                 <Switch
                    id="productIsOnline"
                    checked={currentProductForm.isOnline || false}
                    onCheckedChange={handleFormSwitchChange}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline">{tCommon('cancel')}</Button>
            </DialogClose>
            <Button type="button" onClick={handleAddOrEditProduct} className="bg-accent hover:bg-accent/90 text-accent-foreground">
              {editingProduct ? tCommon('save') : tCommon('add')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AuthenticatedLayout>
  );
}
