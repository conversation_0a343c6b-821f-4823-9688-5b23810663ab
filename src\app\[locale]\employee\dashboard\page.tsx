"use client"; // Make this a client component

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { NotificationIconWithBadge } from '@/components/ui/notification-badge';
import Link from 'next/link';
import { CheckSquare, CreditCard, Truck, Bell, FileText, ListChecks, TrendingUp, Clock, AlertCircle, CheckCircle2, Users, Package } from 'lucide-react';
import { StatsCard, QuickActionCard, PerformanceIndicator } from '@/components/employee/stats-card';
import { useScopedI18n, useI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context'; // Import useAuth
import { useState, useEffect } from 'react';
import { getUnreadNotificationsCount } from '@/lib/mock-notifications-data';

export default function EmployeeDashboardPage() {
  const t = useI18n();
  const tCommon = useScopedI18n('common');
  const { user } = useAuth(); // Get the authenticated user
  const [unreadNotificationsCount, setUnreadNotificationsCount] = useState(0);

  // Load unread notifications count
  useEffect(() => {
    const count = getUnreadNotificationsCount(user?.id);
    setUnreadNotificationsCount(count);
  }, [user?.id]);

  const tasks = [
    {
      titleKey: "employeeDashboard.taskNewOrders",
      count: 5,
      icon: ListChecks,
      color: "text-blue-500",
      bgColor: "stats-card-primary",
      href: "/employee/approve-orders",
      permissionKey: "canReceiveOrders",
      priority: "high",
      description: "طلبات جديدة تحتاج للمراجعة والموافقة"
    },
    {
      titleKey: "employeeDashboard.taskOrdersToDispatch",
      count: 3,
      icon: Truck,
      color: "text-orange-500",
      bgColor: "stats-card-warning",
      href: "/employee/dispatch",
      priority: "medium",
      description: "طلبات جاهزة للإرسال والتوصيل"
    },
    {
      titleKey: "employeeDashboard.taskPendingTransactions",
      count: 2,
      icon: CreditCard,
      color: "text-green-500",
      bgColor: "stats-card-success",
      href: "/employee/transactions",
      priority: "normal",
      description: "معاملات مالية تحتاج للمعالجة"
    },
  ];

  const quickActionsBase = [
    { labelKey: "nav_recordTransactions", href: "/employee/transactions", icon: CreditCard },
    { labelKey: "nav_approveOrders", href: "/employee/approve-orders", icon: ListChecks, permissionKey: "canReceiveOrders" },
    { labelKey: "nav_dispatchOrders", href: "/employee/dispatch", icon: Truck },
    { labelKey: "nav_notifications", href: "/employee/notifications", icon: Bell, showBadge: true, badgeCount: unreadNotificationsCount },
    { labelKey: "nav_tasks", href: "/employee/tasks", icon: FileText },
  ];

  // Filter quick actions based on permissions
  const quickActions = quickActionsBase.filter(action => {
    if (action.permissionKey) {
      return user?.permissions?.[action.permissionKey as keyof typeof user.permissions] === true;
    }
    return true; // No specific permission needed for this action
  });


  const recentActivities = [
    { textKey: "employeeDashboard.activityItemApprovedOrder", params: {orderId: "12345"} },
    { textKey: "employeeDashboard.activityItemRecordedPayment", params: {orderId: "12344"} },
    { textKey: "employeeDashboard.activityItemDispatchedOrder", params: {orderId: "12342"} },
    { textKey: "employeeDashboard.activityItemNewOrderNotification" },
  ];


  return (
    <AuthenticatedLayout expectedRole="employee">
      <div className="space-y-8 p-1">
        {/* Enhanced Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-4xl font-bold text-foreground mb-2">{t('employeeDashboard.title')}</h1>
            <p className="text-lg text-muted-foreground">مرحباً {user?.name || user?.username}، إليك ملخص مهامك اليوم</p>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 px-4 py-2 bg-green-100 dark:bg-green-900 rounded-full">
              <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
              <span className="text-sm font-medium text-green-700 dark:text-green-300">متصل</span>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-blue-100 dark:bg-blue-900 rounded-full">
              <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              <span className="text-sm font-medium text-blue-700 dark:text-blue-300">{new Date().toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}</span>
            </div>
          </div>
        </div>

        {/* Enhanced Task Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {tasks.filter(task => !task.permissionKey || (user?.permissions?.[task.permissionKey as keyof typeof user.permissions] === true)).map((task) => (
            <Card key={task.titleKey} className={`employee-card task-card task-card-${task.priority} group cursor-pointer`}>
              <Link href={task.href} className="block">
                <CardHeader className="employee-card-header">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`p-3 rounded-xl ${task.bgColor} shadow-lg`}>
                        <task.icon className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-lg font-semibold group-hover:text-primary transition-colors">
                          {t(task.titleKey as any)}
                        </CardTitle>
                        <p className="text-sm text-muted-foreground mt-1">{task.description}</p>
                      </div>
                    </div>
                    {task.priority === 'high' && (
                      <AlertCircle className="h-5 w-5 text-red-500 animate-pulse" />
                    )}
                  </div>
                </CardHeader>
                <CardContent className="employee-card-content">
                  <div className="flex items-center justify-between">
                    <div className="text-3xl font-bold text-foreground">{task.count}</div>
                    <Button variant="ghost" size="sm" className="employee-button text-primary hover:bg-primary/10">
                      {t('employeeDashboard.viewTaskAction')}
                      <TrendingUp className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                    </Button>
                  </div>
                  <div className="mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${task.bgColor}`}
                      style={{ width: `${Math.min((task.count / 10) * 100, 100)}%` }}
                    ></div>
                  </div>
                </CardContent>
              </Link>
            </Card>
          ))}
        </div>

        {/* Enhanced Quick Actions */}
        <Card className="employee-card">
          <CardHeader className="employee-card-header">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <TrendingUp className="h-6 w-6 text-primary" />
              </div>
              <div>
                <CardTitle className="text-xl">{t('employeeDashboard.quickActionsTitle')}</CardTitle>
                <CardDescription className="text-base">{t('employeeDashboard.quickActionsDesc')}</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="employee-card-content">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {quickActions.map(action => (
                <Button
                  key={action.labelKey}
                  variant="outline"
                  asChild
                  className="employee-button h-auto p-4 justify-start hover:shadow-lg transition-all duration-200 group"
                >
                  <Link href={action.href} className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 group-hover:bg-primary/20 rounded-lg transition-colors">
                      {action.showBadge && action.badgeCount > 0 ? (
                        <NotificationIconWithBadge
                          icon={<action.icon className="h-5 w-5 text-primary" />}
                          count={action.badgeCount}
                          badgeProps={{ size: 'sm' }}
                        />
                      ) : (
                        <action.icon className="h-5 w-5 text-primary" />
                      )}
                    </div>
                    <div className="text-right rtl:text-left">
                      <div className="font-medium text-foreground group-hover:text-primary transition-colors">
                        {tCommon(action.labelKey as any)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        انقر للوصول السريع
                      </div>
                    </div>
                  </Link>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Performance Summary */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            title="الطلبات المعالجة"
            value="12"
            description="اليوم"
            icon={Package}
            variant="success"
            trend={{ value: 15, isPositive: true }}
          />
          <StatsCard
            title="المعاملات المسجلة"
            value="8"
            description="اليوم"
            icon={CreditCard}
            variant="primary"
            trend={{ value: 8, isPositive: true }}
          />
          <StatsCard
            title="معدل الإنجاز"
            value="95%"
            description="هذا الأسبوع"
            icon={TrendingUp}
            variant="warning"
            trend={{ value: 5, isPositive: true }}
          />
          <StatsCard
            title="الوقت المتوسط"
            value="3.2 دقيقة"
            description="لكل معاملة"
            icon={Clock}
            variant="default"
            trend={{ value: 12, isPositive: false }}
          />
        </div>

        {/* Performance Indicators */}
        <Card className="employee-card">
          <CardHeader className="employee-card-header">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <TrendingUp className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-lg">مؤشرات الأداء</CardTitle>
                <CardDescription>تقدمك نحو أهداف اليوم</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="employee-card-content space-y-6">
            <PerformanceIndicator
              label="هدف الطلبات اليومي"
              current={12}
              target={15}
              unit=" طلب"
            />
            <PerformanceIndicator
              label="هدف المعاملات اليومي"
              current={8}
              target={10}
              unit=" معاملة"
            />
            <PerformanceIndicator
              label="هدف المبيعات اليومي"
              current={15250}
              target={20000}
              unit=" ريال"
            />
          </CardContent>
        </Card>

          {/* Enhanced Recent Activity */}
          <Card className="employee-card">
            <CardHeader className="employee-card-header">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <CardTitle className="text-lg">{t('employeeDashboard.recentActivityTitle')}</CardTitle>
                  <CardDescription>{t('employeeDashboard.recentActivityDesc')}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="employee-card-content">
              <div className="space-y-3">
                {recentActivities.map((activity, index) => (
                  <div key={index} className="employee-notification employee-notification-info">
                    <div className="flex items-center gap-3">
                      <CheckCircle2 className="h-4 w-4 text-primary flex-shrink-0" />
                      <span className="text-sm">{t(activity.textKey as any, activity.params)}</span>
                      <span className="text-xs text-muted-foreground mr-auto">
                        {new Date().toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}
                      </span>
                    </div>
                  </div>
                ))}
                <div className="employee-notification employee-notification-success">
                  <div className="flex items-center gap-3">
                    <CheckCircle2 className="h-4 w-4 text-green-600 flex-shrink-0" />
                    <span className="text-sm">تمت الموافقة على الطلب #ORD123</span>
                    <span className="text-xs text-muted-foreground mr-auto">10:30</span>
                  </div>
                </div>
                <div className="employee-notification employee-notification-info">
                  <div className="flex items-center gap-3">
                    <Package className="h-4 w-4 text-blue-600 flex-shrink-0" />
                    <span className="text-sm">تم إرسال الطلب #ORD122</span>
                    <span className="text-xs text-muted-foreground mr-auto">09:45</span>
                  </div>
                </div>
                <div className="employee-notification employee-notification-warning">
                  <div className="flex items-center gap-3">
                    <CreditCard className="h-4 w-4 text-yellow-600 flex-shrink-0" />
                    <span className="text-sm">تم تسجيل دفعة 15,000 ريال</span>
                    <span className="text-xs text-muted-foreground mr-auto">09:15</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

      </div>
    </AuthenticatedLayout>
  );
}