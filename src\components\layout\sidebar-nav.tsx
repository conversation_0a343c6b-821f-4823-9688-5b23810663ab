
"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import type { NavItem } from "@/config/site";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useI18n } from "@/lib/i18n/client";
import { useAuth } from "@/contexts/auth-context"; // Import useAuth

interface SidebarNavProps {
  items: NavItem[];
  className?: string;
}

export function SidebarNav({ items, className }: SidebarNavProps) {
  const pathname = usePathname();
  const t = useI18n();
  const { user } = useAuth(); // Get user from auth context

  if (!items?.length) {
    return null;
  }

  return (
    <nav className={cn("flex flex-col space-y-2", className)}>
      <div className="mb-4 px-2">
        <h2 className="text-lg font-semibold text-foreground mb-2">القائمة الرئيسية</h2>
        <div className="h-px bg-gradient-to-r from-primary/20 to-transparent"></div>
      </div>

      <ScrollArea className="h-[calc(100vh-12rem)]">
        <div className="space-y-1">
          {items.map((item, index) => {
            if (item.href) {
              // Permission check for employees
              if (user?.role === 'employee' && item.permissionKey) {
                // Item should only be shown if user.permissions exists and the specific permissionKey is explicitly true
                if (!(user.permissions && user.permissions[item.permissionKey] === true)) {
                  return null;
                }
              }

              const isActive = pathname === item.href;

              return (
                <Link key={index} href={item.href} legacyBehavior passHref>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-start mb-1 h-12 px-3 transition-all duration-200 group",
                      isActive
                        ? "bg-gradient-to-r from-primary/10 to-primary/5 text-primary border-r-2 border-primary shadow-sm"
                        : "hover:bg-gradient-to-r hover:from-primary/5 hover:to-transparent hover:text-primary",
                      item.disabled && "cursor-not-allowed opacity-80"
                    )}
                    disabled={item.disabled}
                  >
                    <div className={cn(
                      "p-2 rounded-lg mr-3 rtl:ml-3 rtl:mr-0 transition-colors duration-200",
                      isActive
                        ? "bg-primary/10 text-primary"
                        : "bg-gray-100 dark:bg-gray-800 text-muted-foreground group-hover:bg-primary/10 group-hover:text-primary"
                    )}>
                      <item.icon className="h-4 w-4" />
                    </div>
                    <span className="flex-1 text-sm font-medium">
                      {t(item.title as any)}
                    </span>
                    {item.label && (
                      <span className="ml-auto rounded-lg bg-primary px-2 py-0.5 text-xs text-primary-foreground">
                        {item.label}
                      </span>
                    )}
                    {isActive && (
                      <div className="w-1 h-6 bg-primary rounded-full ml-2 rtl:mr-2 rtl:ml-0"></div>
                    )}
                  </Button>
                </Link>
              );
            }
            return null;
          })}
        </div>
      </ScrollArea>
    </nav>
  );
}
