
"use client"; 

import React, { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Users, UserPlus, MoreHorizontal, Edit, Trash2, Filter, ShieldCheck, Phone } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form";
import type { User, Role, EmployeePermissions } from '@/types';
import { useScopedI18n } from '@/lib/i18n/client';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/auth-context';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { createUser, updateUser, deleteUser as deleteUserAPI, getAllUsers } from '@/lib/auth'; 
import { Checkbox } from '@/components/ui/checkbox';

const ownerCreatableRoles: Role[] = ['employee', 'customer'];

export default function OwnerStaffPage() {
  const t = useScopedI18n('ownerStaff');
  const tCommon = useScopedI18n('common');
  const tUserManagement = useScopedI18n('userManagement');
  const tForm = useScopedI18n('form');
  const { toast } = useToast();
  const { user: loggedInOwner } = useAuth();

  const [managedUsers, setManagedUsers] = useState<User[]>([]);
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [filterText, setFilterText] = useState('');

  const employeePermissionsSchema = z.object({
    canReceiveOrders: z.boolean().default(false),
    canGrantCredit: z.boolean().default(false),
    canManageCustomerBalances: z.boolean().default(false),
    canProcessSalesReturns: z.boolean().default(false),
    canManageProducts: z.boolean().default(false), 
    canSetCreditLimits: z.boolean().default(false), 
    canDispatchOrders: z.boolean().default(false),
    canRecordTransactions: z.boolean().default(false),
    canManageShifts: z.boolean().default(false),
    canViewPerformanceReports: z.boolean().default(false),
    canAccessCommunicationTools: z.boolean().default(false),
    canViewInventoryReports: z.boolean().default(false),
    canPrintReports: z.boolean().default(false),
    canAccessProductLookup: z.boolean().default(false),
    canManageTasks: z.boolean().default(false),
  }).optional();

  const userFormSchema = z.object({
    name: z.string().min(1, tForm('fieldRequired', { field: tUserManagement('name') })),
    username: z.string().min(3, tForm('usernameMin')).optional().or(z.literal('')),
    phoneNumber: z.string()
      .min(9, tForm('phoneNumberInvalid'))
      .max(10, tForm('phoneNumberInvalid'))
      .regex(/^[0-9]+$/, tForm('phoneNumberDigitsOnly')),
    email: z.string().email(tForm('emailInvalid')).optional().or(z.literal('')),
    password: z.string().min(6, tForm('passwordMin')).optional(),
    role: z.enum(['employee', 'customer'], { 
        required_error: tForm('fieldRequired', { field: tUserManagement('role') })
    }),
    permissions: employeePermissionsSchema,
  });

  type UserFormValues = z.infer<typeof userFormSchema>;

  const form = useForm<UserFormValues>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      name: '',
      username: '',
      phoneNumber: '',
      email: '',
      password: '',
      role: 'employee', 
      permissions: {
        canReceiveOrders: false,
        canGrantCredit: false,
        canManageCustomerBalances: false,
        canProcessSalesReturns: false,
        canManageProducts: false,
        canSetCreditLimits: false,
        canDispatchOrders: false,
        canRecordTransactions: false,
        canManageShifts: false,
        canViewPerformanceReports: false,
        canAccessCommunicationTools: false,
        canViewInventoryReports: false,
        canPrintReports: false,
        canAccessProductLookup: false,
        canManageTasks: false,
      }
    },
  });

  useEffect(() => {
    if (loggedInOwner) {
      fetchManagedUsers();
    }
  }, [loggedInOwner]);

  const fetchManagedUsers = async () => {
    if (!loggedInOwner) return;
    try {
      const allSysUsers = await getAllUsers(); 
      setManagedUsers(allSysUsers.filter(u => u.createdById === loggedInOwner.id)); 
    } catch (error) {
      toast({ title: tCommon('error'), description: t('errorFetchingUsers'), variant: "destructive" });
    }
  };


  const getUserRoleDisplay = (userToDisplay: User) => {
    if (userToDisplay.role === 'employee') {
      return tUserManagement(`role_${userToDisplay.role}` as any, undefined, userToDisplay.role);
    }
    return tUserManagement(`role_${userToDisplay.role}` as any, undefined, userToDisplay.role);
  };

  const handleOpenAddModal = () => {
    setEditingUser(null);
    form.reset({ 
      name: '', 
      username: '', 
      phoneNumber: '',
      email: '', 
      password: '', 
      role: 'employee',
      permissions: { 
        canReceiveOrders: false,
        canGrantCredit: false,
        canManageCustomerBalances: false,
        canProcessSalesReturns: false,
        canManageProducts: false,
        canSetCreditLimits: false,
        canDispatchOrders: false,
        canRecordTransactions: false,
        canManageShifts: false,
        canViewPerformanceReports: false,
        canAccessCommunicationTools: false,
        canViewInventoryReports: false,
        canPrintReports: false,
        canAccessProductLookup: false,
        canManageTasks: false,
      }
    });
    setIsUserModalOpen(true);
  };

  const handleOpenEditModal = (userToEdit: User) => {
    setEditingUser(userToEdit);
    form.reset({
      name: userToEdit.name || '',
      username: userToEdit.username || '',
      phoneNumber: userToEdit.phoneNumber || '',
      email: userToEdit.email || '',
      password: '', 
      role: userToEdit.role as 'employee' | 'customer', 
      permissions: userToEdit.role === 'employee' ? {
        canReceiveOrders: userToEdit.permissions?.canReceiveOrders || false,
        canGrantCredit: userToEdit.permissions?.canGrantCredit || false,
        canManageCustomerBalances: userToEdit.permissions?.canManageCustomerBalances || false,
        canProcessSalesReturns: userToEdit.permissions?.canProcessSalesReturns || false,
        canManageProducts: userToEdit.permissions?.canManageProducts || false,
        canSetCreditLimits: userToEdit.permissions?.canSetCreditLimits || false,
        canDispatchOrders: userToEdit.permissions?.canDispatchOrders || false,
        canRecordTransactions: userToEdit.permissions?.canRecordTransactions || false,
        canManageShifts: userToEdit.permissions?.canManageShifts || false,
        canViewPerformanceReports: userToEdit.permissions?.canViewPerformanceReports || false,
        canAccessCommunicationTools: userToEdit.permissions?.canAccessCommunicationTools || false,
        canViewInventoryReports: userToEdit.permissions?.canViewInventoryReports || false,
        canPrintReports: userToEdit.permissions?.canPrintReports || false,
        canAccessProductLookup: userToEdit.permissions?.canAccessProductLookup || false,
        canManageTasks: userToEdit.permissions?.canManageTasks || false,
      } : { 
        canReceiveOrders: false,
        canGrantCredit: false,
        canManageCustomerBalances: false,
        canProcessSalesReturns: false,
        canManageProducts: false,
        canSetCreditLimits: false,
        canDispatchOrders: false,
        canRecordTransactions: false,
        canManageShifts: false,
        canViewPerformanceReports: false,
        canAccessCommunicationTools: false,
        canViewInventoryReports: false,
        canPrintReports: false,
        canAccessProductLookup: false,
        canManageTasks: false,
      }
    });
    setIsUserModalOpen(true);
  };

  const handleUserFormSubmit = async (values: UserFormValues) => {
    if (!loggedInOwner) {
      toast({ title: tCommon('error'), description: "Owner not authenticated.", variant: "destructive" });
      return;
    }

    try {
      const userData: Partial<User> = { 
        ...values, 
        email: values.email || undefined, // Ensure email is undefined if empty
        username: values.username || values.phoneNumber, // Default username to phone if empty
      };
      if (values.role !== 'employee') {
        delete userData.permissions;
      } else {
        userData.permissions = {
            canReceiveOrders: values.permissions?.canReceiveOrders || false,
            canGrantCredit: values.permissions?.canGrantCredit || false,
            canManageCustomerBalances: values.permissions?.canManageCustomerBalances || false,
            canProcessSalesReturns: values.permissions?.canProcessSalesReturns || false,
            canManageProducts: values.permissions?.canManageProducts || false,
            canSetCreditLimits: values.permissions?.canSetCreditLimits || false,
            canDispatchOrders: values.permissions?.canDispatchOrders || false,
            canRecordTransactions: values.permissions?.canRecordTransactions || false,
            canManageShifts: values.permissions?.canManageShifts || false,
            canViewPerformanceReports: values.permissions?.canViewPerformanceReports || false,
            canAccessCommunicationTools: values.permissions?.canAccessCommunicationTools || false,
            canViewInventoryReports: values.permissions?.canViewInventoryReports || false,
            canPrintReports: values.permissions?.canPrintReports || false,
            canAccessProductLookup: values.permissions?.canAccessProductLookup || false,
            canManageTasks: values.permissions?.canManageTasks || false,
        };
      }


      if (editingUser) {
        if (!values.password) {
          // @ts-ignore
          delete userData.password; 
        }
        await updateUser({ ...editingUser, ...userData });
        toast({ title: tCommon('success'), description: t('staffMemberUpdatedSuccess') });
      } else {
        if (!values.password) {
            toast({ title: tCommon('error'), description: tUserManagement('passwordRequiredForNew'), variant: "destructive" });
            return;
        }
        await createUser({ 
          ...(userData as Omit<User, 'id'>), 
          createdById: loggedInOwner.id 
        });
        toast({ title: tCommon('success'), description: t('staffMemberAddedSuccess') });
      }
      setIsUserModalOpen(false);
      fetchManagedUsers();
    } catch (error) {
       toast({ title: tCommon('error'), description: (error as Error).message || t('errorProcessingUser'), variant: "destructive" });
    }
  };

  const handleDeleteManagedUser = async (userId: string) => {
    if (window.confirm(t('confirmRemoveEmployee'))) { 
      try {
        await deleteUserAPI(userId);
        toast({ title: tCommon('success'), description: t('staffMemberRemovedSuccess'), variant: 'destructive' });
        fetchManagedUsers();
      } catch (error) {
        toast({ title: tCommon('error'), description: (error as Error).message || t('errorDeletingUser'), variant: "destructive" });
      }
    }
  };

  const filteredManagedUsers = managedUsers.filter(
    u =>
      (u.name && u.name.toLowerCase().includes(filterText.toLowerCase())) ||
      (u.username && u.username.toLowerCase().includes(filterText.toLowerCase())) ||
      (u.phoneNumber && u.phoneNumber.includes(filterText)) ||
      (u.email && u.email.toLowerCase().includes(filterText.toLowerCase()))
  );

  const watchedRole = form.watch('role');


  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-8 p-1">
        {/* Enhanced Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
          <div className="flex items-center space-x-4">
            <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg">
              <Users className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                {t('title')}
              </h1>
              <p className="text-lg text-muted-foreground mt-1">
                {t('description')}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Badge variant="outline" className="text-blue-600 border-blue-200 bg-blue-50">
              <Users className="h-3 w-3 mr-1" />
              {filteredManagedUsers.length} موظف
            </Badge>
            <Button
              onClick={handleOpenAddModal}
              className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <UserPlus className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
              {t('addNewEmployeeButton')}
            </Button>
          </div>
        </div>

        {/* Enhanced Employee List Card */}
        <Card className="shadow-xl border-0 overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900">
                  <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <CardTitle className="text-xl">{t('employeeListTitle')}</CardTitle>
                  <CardDescription className="text-base">
                    {t('employeeListDescription')}
                  </CardDescription>
                </div>
              </div>
              <Badge variant="secondary" className="text-sm px-3 py-1">
                {filteredManagedUsers.length} من {managedUsers.length}
              </Badge>
            </div>
            <div className="flex items-center space-x-3 pt-4 rtl:space-x-reverse">
              <div className="relative flex-1 max-w-sm">
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t('filterPlaceholder')}
                  className="pl-10 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={filterText}
                  onChange={(e) => setFilterText(e.target.value)}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {filteredManagedUsers.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">{t('noStaffMembers')}</p>
            ) : (
            <div className="rounded-md border">
                <Table>
                <TableHeader>
                    <TableRow>
                    <TableHead>{tUserManagement('name')}</TableHead>
                    <TableHead>{tUserManagement('phoneNumber')}</TableHead>
                    <TableHead>{tUserManagement('username')}</TableHead>
                    <TableHead>{tUserManagement('email')}</TableHead>
                    <TableHead>{t('rolePositionHeader')}</TableHead>
                    <TableHead className="text-right rtl:text-left">{tCommon('actions')}</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {filteredManagedUsers.map((userAcc) => (
                    <TableRow key={userAcc.id}>
                        <TableCell className="font-medium">{userAcc.name || tCommon('N_A')}</TableCell>
                        <TableCell>{userAcc.phoneNumber || tCommon('N_A')}</TableCell>
                        <TableCell>{userAcc.username || tCommon('N_A')}</TableCell>
                        <TableCell>{userAcc.email || tCommon('N_A')}</TableCell>
                        <TableCell><Badge variant="secondary">{getUserRoleDisplay(userAcc)}</Badge></TableCell>
                        <TableCell className="text-right rtl:text-left">
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">{tCommon('actions')}</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>{tCommon('actions')}</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleOpenEditModal(userAcc)}>
                                  <Edit className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('editDetailsItem')}
                              </DropdownMenuItem>
                              {userAcc.role === 'employee' && (
                                <DropdownMenuItem onClick={() => handleOpenEditModal(userAcc)}> 
                                    <ShieldCheck className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('managePermissionsItem')}
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleDeleteManagedUser(userAcc.id)} className="text-destructive focus:text-destructive focus:bg-destructive/10">
                                  <Trash2 className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {t('removeEmployeeItem')}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                        </TableCell>
                    </TableRow>
                    ))}
                </TableBody>
                </Table>
            </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Dialog open={isUserModalOpen} onOpenChange={setIsUserModalOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>{editingUser ? t('editStaffMemberTitle') : t('addStaffMemberTitle')}</DialogTitle>
            <DialogDescription>
              {editingUser ? t('editStaffMemberDesc') : t('addStaffMemberDesc')}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleUserFormSubmit)} className="space-y-4 py-2 max-h-[70vh] overflow-y-auto pr-2">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{tUserManagement('name')}</FormLabel>
                    <FormControl>
                      <Input placeholder={tUserManagement('name')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
               <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{tUserManagement('phoneNumber')}</FormLabel>
                    <FormControl>
                      <Input type="tel" placeholder={tForm('phoneNumberPlaceholder')} {...field} />
                    </FormControl>
                    <FormDescription>{tForm('phoneNumberHelpNoPrefix')}</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{tUserManagement('username')} ({tCommon('optional')})</FormLabel>
                    <FormControl>
                      <Input placeholder={tUserManagement('username')} {...field} />
                    </FormControl>
                    <FormDescription>{tForm('usernameOptionalHelp')}</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{tUserManagement('email')} ({tCommon('optional')})</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder={tUserManagement('email')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{tUserManagement('password')}</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder={editingUser ? tUserManagement('passwordPlaceholderEdit') : tUserManagement('passwordPlaceholderCreate')} {...field} />
                    </FormControl>
                    <FormDescription>
                      {editingUser ? t('passwordLeaveBlankDescription') : t('passwordRequiredForNewStaff')}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                    <FormItem>
                        <FormLabel>{tUserManagement('role')}</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                                <SelectTrigger>
                                    <SelectValue placeholder={tUserManagement('selectRole')} />
                                </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                                {ownerCreatableRoles.map(r => (
                                    <SelectItem key={r} value={r}>
                                        {tUserManagement(`role_${r}` as any, undefined, r)}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <FormMessage />
                    </FormItem>
                )}
              />

              {watchedRole === 'employee' && (
                <Card className="p-4 mt-4">
                  <CardHeader className="p-0 pb-2">
                    <CardTitle className="text-md">{t('permissionsTitle')}</CardTitle>
                    <CardDescription>{t('permissionsForEmployeeRole')}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3 p-0 pt-2">
                    {[
                        {name: 'permissions.canReceiveOrders', label: t('permissionCanReceiveOrders')},
                        {name: 'permissions.canGrantCredit', label: t('permissionCanGrantCredit')},
                        {name: 'permissions.canManageCustomerBalances', label: t('permissionCanManageCustomerBalances')},
                        {name: 'permissions.canProcessSalesReturns', label: t('permissionCanProcessSalesReturns')},
                        {name: 'permissions.canManageProducts', label: t('permissionCanManageProducts')},
                        {name: 'permissions.canSetCreditLimits', label: t('permissionCanSetCreditLimits')},
                        {name: 'permissions.canDispatchOrders', label: t('permissionCanDispatchOrders')},
                        {name: 'permissions.canRecordTransactions', label: t('permissionCanRecordTransactions')},
                        {name: 'permissions.canManageShifts', label: t('permissionCanManageShifts')},
                        {name: 'permissions.canViewPerformanceReports', label: t('permissionCanViewPerformanceReports')},
                        {name: 'permissions.canAccessCommunicationTools', label: t('permissionCanAccessCommunicationTools')},
                        {name: 'permissions.canViewInventoryReports', label: t('permissionCanViewInventoryReports')},
                        {name: 'permissions.canPrintReports', label: t('permissionCanPrintReports')},
                        {name: 'permissions.canAccessProductLookup', label: t('permissionCanAccessProductLookup')},
                        {name: 'permissions.canManageTasks', label: t('permissionCanManageTasks')},
                    ].map(perm => (
                         <FormField
                            key={perm.name}
                            control={form.control}
                            name={perm.name as keyof UserFormValues['permissions']}
                            render={({ field }) => (
                                <FormItem className="flex flex-row items-center space-x-3 space-y-0 rtl:space-x-reverse">
                                <FormControl>
                                    <Checkbox checked={field.value} onCheckedChange={field.onChange} id={perm.name}/>
                                </FormControl>
                                <FormLabel htmlFor={perm.name} className="font-normal">{perm.label}</FormLabel>
                                </FormItem>
                            )}
                        />
                    ))}
                  </CardContent>
                </Card>
              )}


              <DialogFooter>
                <DialogClose asChild>
                  <Button type="button" variant="outline">{tCommon('cancel')}</Button>
                </DialogClose>
                <Button type="submit" className="bg-accent hover:bg-accent/90 text-accent-foreground" disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting ? tUserManagement('saving') : (editingUser ? tCommon('save') : tCommon('add'))}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

    </AuthenticatedLayout>
  );
}

