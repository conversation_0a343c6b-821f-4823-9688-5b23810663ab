"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON>le, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Activity,
  Calendar,
  Download,
  RefreshCw
} from 'lucide-react';
import { useState } from 'react';

interface ChartData {
  name: string;
  value: number;
  change: number;
}

interface EnhancedChartProps {
  title: string;
  description?: string;
  data?: ChartData[];
  type?: 'bar' | 'line' | 'area';
  timeframe?: 'day' | 'week' | 'month' | 'year';
}

export function EnhancedChart({ 
  title, 
  description = "إحصائيات الأداء", 
  data = [],
  type = 'bar',
  timeframe = 'week'
}: EnhancedChartProps) {
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframe);
  const [isLoading, setIsLoading] = useState(false);

  // Mock data for demonstration
  const mockData = [
    { name: 'السبت', value: 2400, change: 12 },
    { name: 'الأحد', value: 1398, change: -5 },
    { name: 'الاثنين', value: 9800, change: 18 },
    { name: 'الثلاثاء', value: 3908, change: 8 },
    { name: 'الأربعاء', value: 4800, change: 15 },
    { name: 'الخميس', value: 3800, change: -3 },
    { name: 'الجمعة', value: 4300, change: 22 }
  ];

  const chartData = data.length > 0 ? data : mockData;
  const maxValue = Math.max(...chartData.map(d => d.value));
  const totalValue = chartData.reduce((sum, d) => sum + d.value, 0);
  const averageChange = chartData.reduce((sum, d) => sum + d.change, 0) / chartData.length;

  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 1000);
  };

  const timeframeOptions = [
    { value: 'day', label: 'يومي' },
    { value: 'week', label: 'أسبوعي' },
    { value: 'month', label: 'شهري' },
    { value: 'year', label: 'سنوي' }
  ];

  return (
    <Card className="shadow-lg border-0 overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900">
              <BarChart3 className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <CardTitle className="text-xl">{title}</CardTitle>
              <CardDescription className="text-base">{description}</CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={averageChange >= 0 ? "default" : "destructive"} className="text-sm">
              {averageChange >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1" />
              )}
              {averageChange.toFixed(1)}%
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
        
        {/* Timeframe Selector */}
        <div className="flex items-center space-x-2 pt-3">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <div className="flex space-x-1">
            {timeframeOptions.map((option) => (
              <Button
                key={option.value}
                variant={selectedTimeframe === option.value ? "default" : "ghost"}
                size="sm"
                onClick={() => setSelectedTimeframe(option.value as any)}
                className="h-7 px-3 text-xs"
              >
                {option.label}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
            <div className="text-lg font-bold text-foreground">
              {totalValue.toLocaleString()}
            </div>
            <div className="text-xs text-muted-foreground">إجمالي المبيعات</div>
          </div>
          <div className="text-center p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
            <div className="text-lg font-bold text-foreground">
              {(totalValue / chartData.length).toLocaleString()}
            </div>
            <div className="text-xs text-muted-foreground">المتوسط اليومي</div>
          </div>
          <div className="text-center p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
            <div className="text-lg font-bold text-foreground">
              {maxValue.toLocaleString()}
            </div>
            <div className="text-xs text-muted-foreground">أعلى قيمة</div>
          </div>
        </div>

        {/* Chart Visualization */}
        <div className="space-y-3">
          {chartData.map((item, index) => (
            <div key={item.name} className="flex items-center space-x-3">
              <div className="w-16 text-sm text-muted-foreground text-right">
                {item.name}
              </div>
              <div className="flex-1 relative">
                <div className="h-8 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg transition-all duration-1000 ease-out"
                    style={{
                      width: `${(item.value / maxValue) * 100}%`,
                      animationDelay: `${index * 100}ms`
                    }}
                  />
                </div>
                <div className="absolute inset-y-0 left-3 flex items-center">
                  <span className="text-xs font-medium text-white">
                    {item.value.toLocaleString()}
                  </span>
                </div>
              </div>
              <div className={`flex items-center text-xs font-medium px-2 py-1 rounded-full ${
                item.change >= 0 
                  ? 'text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/30' 
                  : 'text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/30'
              }`}>
                {item.change >= 0 ? (
                  <TrendingUp className="h-3 w-3 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1" />
                )}
                {item.change}%
              </div>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between mt-6 pt-4 border-t">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Activity className="h-4 w-4" />
            <span>آخر تحديث: منذ دقيقتين</span>
          </div>
          <Button variant="outline" size="sm" className="text-xs">
            <Download className="h-3 w-3 mr-1" />
            تصدير البيانات
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
