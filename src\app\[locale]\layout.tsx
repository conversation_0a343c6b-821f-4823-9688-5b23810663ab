
import type { Metadata, Viewport } from 'next';
import { Inter } from 'next/font/google';
import '../globals.css'; // Path relative to src/app/[locale]/
import '../../styles/employee-enhancements.css';
import { Toaster } from '@/components/ui/toaster';
import { AuthProvider } from '@/contexts/auth-context';
import { ThemeProvider } from '@/components/theme-provider';
import { I18nProviderClient } from '@/lib/i18n/client';
import type { Locale } from '@/lib/i18n/config';
import { i18nConfig } from '@/lib/i18n/config';
import { getScopedI18n, getStaticParams as i18nGetStaticParams } from '@/lib/i18n/server';

const inter = Inter({
  subsets: ['latin', 'arabic'],
  variable: '--font-inter',
});

// export const metadata: Metadata = { // Metadata will be set using translations
//   title: 'MarketSync - Supermarket Management',
//   description: 'Centralized and secure supermarket operations management.',
// };

export async function generateMetadata({ params }: { params: Promise<{ locale: Locale }> }): Promise<Metadata> {
  const { locale } = await params;
  const t = await getScopedI18n(locale, 'common');
  return {
    title: t('appName'),
    description: t('appDescription'),
  };
}

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
}

// Inform Next.js about the available locales for static generation
export function generateStaticParams() {
  // return i18nConfig.locales.map(locale => ({ locale }));
  return i18nGetStaticParams(); // Use the function from next-international
}

export default async function LocaleLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: Locale }>;
}>) {
  const { locale } = await params;
  // The middleware should ensure 'locale' is always 'ar'.
  const activeLocale = 'ar';

  return (
    <html lang={activeLocale} dir="rtl" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <I18nProviderClient locale={activeLocale}>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <AuthProvider>
              {children}
              <Toaster />
            </AuthProvider>
          </ThemeProvider>
        </I18nProviderClient>
      </body>
    </html>
  );
}
