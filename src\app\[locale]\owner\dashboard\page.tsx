
"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { PlaceholderChart } from '@/components/dashboard/placeholder-chart';
import { Enhanced<PERSON>hart } from '@/components/dashboard/enhanced-chart';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import Link from 'next/link';
import {
  Users,
  Package,
  ShoppingCart,
  BarChart3,
  DollarSign,
  Briefcase,
  BookText,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Activity,
  Clock,
  Star,
  Target,
  Zap,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import { useAuth } from '@/contexts/auth-context';
import { getMockProducts } from '@/lib/mock-product-data';
import type { Debt } from '@/types';
import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, AlertTitle, AlertDescription } from "@/components/ui/alert"; // Import Alert components

// Helper to get debts from localStorage, similar to products
const getStoredDebts = (): Debt[] => {
  if (typeof window === 'undefined') return []; 
  const stored = localStorage.getItem('marketSyncOwnerDebts'); 
  return stored ? JSON.parse(stored) : [];
};


export default function OwnerDashboardPage() {
  const t = useScopedI18n('ownerDashboard');
  const tCommon = useScopedI18n('common');
  const { user } = useAuth();
  const [productsCount, setProductsCount] = useState(0);
  const [totalDebtsValue, setTotalDebtsValue] = useState(0);
  const [isSubscriptionExpired, setIsSubscriptionExpired] = useState(false);

  useEffect(() => {
    if (user && typeof window !== 'undefined') {
      const ownerProducts = getMockProducts().filter(p => p.ownerId === user.id);
      setProductsCount(ownerProducts.length);

      const ownerDebts = getStoredDebts().filter(d => d.ownerId === user.id && d.type === 'debt' && d.status === 'unpaid');
      const debtsSum = ownerDebts.reduce((sum, debt) => sum + debt.amount, 0);
      setTotalDebtsValue(debtsSum);

      if (user.subscriptionEndDate) {
        const endDate = new Date(user.subscriptionEndDate);
        const today = new Date();
        // Set time to 00:00:00 for accurate date comparison, ensuring expiry happens at end of day
        endDate.setHours(23, 59, 59, 999); 
        today.setHours(0,0,0,0);
        setIsSubscriptionExpired(endDate < today);
      } else {
        // If no subscription end date, assume it's not relevant or always active for this mock
        setIsSubscriptionExpired(false); 
      }
    }
  }, [user]);


  const stats = [
    {
      titleKey: "totalStaff",
      value: "15",
      icon: Users,
      color: "text-blue-500",
      bgColor: "bg-blue-50 dark:bg-blue-950",
      href: "/owner/staff",
      change: "+2",
      changeType: "increase",
      description: "موظف جديد هذا الشهر"
    },
    {
      titleKey: "productsListed",
      value: productsCount.toString(),
      icon: Package,
      color: "text-purple-500",
      bgColor: "bg-purple-50 dark:bg-purple-950",
      href: "/owner/products",
      change: "+12",
      changeType: "increase",
      description: "منتج جديد هذا الأسبوع"
    },
    {
      titleKey: "todaysSales",
      value: `YER 2,500`,
      icon: DollarSign,
      color: "text-green-500",
      bgColor: "bg-green-50 dark:bg-green-950",
      href: "/owner/reports",
      change: "+15.2%",
      changeType: "increase",
      description: "مقارنة بالأمس"
    },
    {
      titleKey: "pendingOrders",
      value: "8",
      icon: ShoppingCart,
      color: "text-orange-500",
      bgColor: "bg-orange-50 dark:bg-orange-950",
      href: "/owner/purchases",
      change: "-3",
      changeType: "decrease",
      description: "طلبات أقل من الأمس"
    },
    {
      titleKey: "totalDebts",
      value: `YER ${totalDebtsValue.toFixed(2)}`,
      icon: BookText,
      color: "text-red-500",
      bgColor: "bg-red-50 dark:bg-red-950",
      href: "/owner/debts",
      change: "-5.8%",
      changeType: "decrease",
      description: "انخفاض في الديون"
    },
  ];

  const quickActions = [
    {
      labelKey: "manageStaff",
      href: "/owner/staff",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-50 hover:bg-blue-100 dark:bg-blue-950 dark:hover:bg-blue-900",
      description: "إدارة الموظفين والصلاحيات"
    },
    {
      labelKey: "manageProducts",
      href: "/owner/products",
      icon: Package,
      color: "text-purple-600",
      bgColor: "bg-purple-50 hover:bg-purple-100 dark:bg-purple-950 dark:hover:bg-purple-900",
      description: "إضافة وتعديل المنتجات"
    },
    {
      labelKey: "viewSalesReports",
      href: "/owner/reports",
      icon: BarChart3,
      color: "text-green-600",
      bgColor: "bg-green-50 hover:bg-green-100 dark:bg-green-950 dark:hover:bg-green-900",
      description: "تقارير المبيعات والأرباح"
    },
    {
      labelKey: "inventoryCheck",
      href: "/owner/inventory",
      icon: Briefcase,
      color: "text-orange-600",
      bgColor: "bg-orange-50 hover:bg-orange-100 dark:bg-orange-950 dark:hover:bg-orange-900",
      description: "مراقبة المخزون والكميات"
    },
    {
      labelKey: "aiDemandForecast",
      href: "/owner/predictions",
      icon: Zap,
      color: "text-indigo-600",
      bgColor: "bg-indigo-50 hover:bg-indigo-100 dark:bg-indigo-950 dark:hover:bg-indigo-900",
      description: "توقعات الطلب بالذكاء الاصطناعي"
    },
    {
      labelKey: "manageDebts",
      href: "/owner/debts",
      icon: BookText,
      color: "text-red-600",
      bgColor: "bg-red-50 hover:bg-red-100 dark:bg-red-950 dark:hover:bg-red-900",
      description: "متابعة الديون والمدفوعات"
    }
  ];

  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-8 p-1">
        {/* Enhanced Header Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-lg">
              <BarChart3 className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {t('title')}
              </h1>
              <p className="text-lg text-muted-foreground mt-1">
                {t('description')}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50">
              <Activity className="h-3 w-3 mr-1" />
              نشط
            </Badge>
            <Badge variant="outline" className="text-blue-600 border-blue-200 bg-blue-50">
              <Clock className="h-3 w-3 mr-1" />
              {new Date().toLocaleDateString('ar-SA')}
            </Badge>
          </div>
        </div>

        {isSubscriptionExpired && (
          <Alert variant="destructive" className="shadow-lg border-l-4 border-l-red-500">
            <AlertTriangle className="h-5 w-5" />
            <AlertTitle className="text-lg font-semibold">{tCommon('subscriptionExpired')}</AlertTitle>
            <AlertDescription className="text-base">
              {tCommon('renewSubscriptionPrompt')}
              <Button asChild variant="link" className="p-0 h-auto text-destructive-foreground hover:text-destructive-foreground/80 font-semibold ml-1 rtl:mr-1 rtl:ml-0">
                <Link href="/owner/subscription">{tCommon('manageSubscriptionNow')}</Link>
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Enhanced Stats Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
          {stats.map((stat) => (
            <Link key={stat.titleKey} href={stat.href}>
              <Card className="group hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 shadow-lg overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                  <div className="space-y-1 flex-1">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      {t(stat.titleKey as any)}
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                      <div className="text-2xl font-bold text-foreground">{stat.value}</div>
                      <div className={`flex items-center text-xs font-medium px-2 py-1 rounded-full ${
                        stat.changeType === 'increase'
                          ? 'text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/30'
                          : 'text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/30'
                      }`}>
                        {stat.changeType === 'increase' ? (
                          <TrendingUp className="h-3 w-3 mr-1" />
                        ) : (
                          <TrendingDown className="h-3 w-3 mr-1" />
                        )}
                        {stat.change}
                      </div>
                    </div>
                  </div>
                  <div className={`p-3 rounded-xl ${stat.bgColor} group-hover:scale-110 transition-transform duration-300 shadow-sm`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-xs text-muted-foreground mb-2">{stat.description}</p>
                  <div className="flex items-center text-xs text-primary font-medium group-hover:text-primary/80">
                    عرض التفاصيل
                    <ArrowUpRight className="h-3 w-3 mr-1 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform" />
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* Enhanced Quick Actions and Chart */}
        <div className="grid gap-8 md:grid-cols-2">
          <Card className="shadow-lg border-0">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-600 text-white">
                  <Zap className="h-5 w-5" />
                </div>
                <div>
                  <CardTitle className="text-xl">{t('quickActions')}</CardTitle>
                  <CardDescription className="text-base">{t('quickActionsDescription')}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="grid grid-cols-1 gap-4">
              {quickActions.map(action => (
                <Link key={action.labelKey} href={action.href}>
                  <div className={`group p-4 rounded-xl ${action.bgColor} transition-all duration-300 hover:shadow-md border border-transparent hover:border-gray-200 dark:hover:border-gray-700`}>
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-lg bg-white/80 dark:bg-gray-800/80 shadow-sm">
                        <action.icon className={`h-5 w-5 ${action.color}`} />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                          {t(action.labelKey as any)}
                        </h3>
                        <p className="text-sm text-muted-foreground">{action.description}</p>
                      </div>
                      <ArrowUpRight className="h-4 w-4 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 group-hover:-translate-y-1 transition-all" />
                    </div>
                  </div>
                </Link>
              ))}
            </CardContent>
          </Card>

          {/* Enhanced Chart Card */}
          <Card className="shadow-lg border-0">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-green-500 to-teal-600 text-white">
                  <BarChart3 className="h-5 w-5" />
                </div>
                <div>
                  <CardTitle className="text-xl">نظرة عامة على الأداء</CardTitle>
                  <CardDescription className="text-base">إحصائيات المبيعات والأرباح</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <EnhancedChart
                title="أداء المبيعات"
                description="إحصائيات المبيعات الأسبوعية"
              />
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Subscription Status */}
        <Card className={`shadow-lg border-0 ${isSubscriptionExpired ? 'bg-red-50 dark:bg-red-950/20' : 'bg-green-50 dark:bg-green-950/20'}`}>
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg ${isSubscriptionExpired ? 'bg-red-500' : 'bg-green-500'} text-white`}>
                  {isSubscriptionExpired ? <AlertTriangle className="h-5 w-5" /> : <Star className="h-5 w-5" />}
                </div>
                <div>
                  <CardTitle className="text-xl">{t('subscriptionStatus')}</CardTitle>
                  <CardDescription className="text-base">
                    {isSubscriptionExpired ? 'يتطلب تجديد الاشتراك' : 'الاشتراك نشط ومفعل'}
                  </CardDescription>
                </div>
              </div>
              <Badge variant={isSubscriptionExpired ? "destructive" : "default"} className="text-sm px-3 py-1">
                {isSubscriptionExpired ? 'منتهي الصلاحية' : 'نشط'}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className={`p-4 rounded-lg ${isSubscriptionExpired ? 'bg-red-100 dark:bg-red-900/30' : 'bg-green-100 dark:bg-green-900/30'}`}>
                {isSubscriptionExpired ? (
                  <p className="text-red-700 dark:text-red-400 font-semibold">{t('subscriptionExpiredTitle')}</p>
                ) : (
                  <p className="text-green-700 dark:text-green-400 font-semibold">{t('currentSubscriptionActive')}</p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  {isSubscriptionExpired ? t('renewPrompt') : t('renewsOn', {date: user?.subscriptionEndDate ? new Date(user.subscriptionEndDate).toLocaleDateString() : tCommon('N_A')})}
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">خطة المالك المتقدمة</span>
                </div>
                <Button
                  variant={isSubscriptionExpired ? "destructive" : "default"}
                  asChild
                  className="shadow-md hover:shadow-lg transition-shadow"
                >
                  <Link href="/owner/subscription">
                    {isSubscriptionExpired ? tCommon('manageSubscriptionNow') : t('manageSubscription')}
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

      </div>
    </AuthenticatedLayout>
  );
}

