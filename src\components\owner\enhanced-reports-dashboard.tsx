"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  DollarSign, 
  ShoppingCart, 
  TrendingUp,
  TrendingDown,
  Users,
  Package,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  Target,
  Activity,
  PieChart,
  LineChart
} from 'lucide-react';
import { EnhancedChart } from '@/components/dashboard/enhanced-chart';

interface ReportStats {
  totalSales: string;
  totalOrders: string;
  averageOrderValue: string;
  totalCustomers: string;
  topProducts: string;
  conversionRate: string;
}

interface EnhancedReportsDashboardProps {
  stats: ReportStats;
  t: (key: string) => string;
  onGenerateReport: (type: string) => void;
}

export function EnhancedReportsDashboard({ 
  stats, 
  t, 
  onGenerateReport 
}: EnhancedReportsDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 1500);
  };

  const reportCards = [
    {
      title: t('totalSales'),
      value: stats.totalSales,
      change: '+10.2%',
      changeType: 'increase' as const,
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-950/50',
      description: 'إجمالي المبيعات هذا الشهر'
    },
    {
      title: t('totalOrders'),
      value: stats.totalOrders,
      change: '+5.5%',
      changeType: 'increase' as const,
      icon: ShoppingCart,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-950/50',
      description: 'عدد الطلبات المكتملة'
    },
    {
      title: t('averageOrderValue'),
      value: stats.averageOrderValue,
      change: '+2.1%',
      changeType: 'increase' as const,
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 dark:bg-purple-950/50',
      description: 'متوسط قيمة الطلب'
    },
    {
      title: 'إجمالي العملاء',
      value: stats.totalCustomers || '1,234',
      change: '+8.3%',
      changeType: 'increase' as const,
      icon: Users,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50 dark:bg-indigo-950/50',
      description: 'عدد العملاء النشطين'
    },
    {
      title: 'أفضل المنتجات',
      value: stats.topProducts || '45',
      change: '+12.7%',
      changeType: 'increase' as const,
      icon: Package,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50 dark:bg-orange-950/50',
      description: 'المنتجات الأكثر مبيعاً'
    },
    {
      title: 'معدل التحويل',
      value: stats.conversionRate || '3.2%',
      change: '+0.8%',
      changeType: 'increase' as const,
      icon: Target,
      color: 'text-pink-600',
      bgColor: 'bg-pink-50 dark:bg-pink-950/50',
      description: 'معدل تحويل الزوار لعملاء'
    }
  ];

  const reportTypes = [
    {
      title: 'تقرير يومي',
      description: 'إحصائيات اليوم الحالي',
      icon: Calendar,
      color: 'bg-blue-500',
      type: 'daily'
    },
    {
      title: 'تقرير أسبوعي',
      description: 'إحصائيات الأسبوع الحالي',
      icon: BarChart3,
      color: 'bg-green-500',
      type: 'weekly'
    },
    {
      title: 'تقرير شهري',
      description: 'إحصائيات الشهر الحالي',
      icon: PieChart,
      color: 'bg-purple-500',
      type: 'monthly'
    },
    {
      title: 'تقرير سنوي',
      description: 'إحصائيات السنة الحالية',
      icon: LineChart,
      color: 'bg-orange-500',
      type: 'yearly'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Header with Actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-foreground">لوحة التقارير المتقدمة</h2>
          <p className="text-muted-foreground">تحليل شامل لأداء المبيعات والعمليات</p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50">
            <Activity className="h-3 w-3 mr-1" />
            تحديث مباشر
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            فلترة
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {reportCards.map((card, index) => (
          <Card 
            key={card.title} 
            className="shadow-lg border-0 overflow-hidden group hover:shadow-xl transition-all duration-300"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-3 ${card.bgColor}`}>
              <div>
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {card.title}
                </CardTitle>
                <div className="text-2xl font-bold text-foreground mt-1">
                  {card.value}
                </div>
              </div>
              <div className="p-3 rounded-xl bg-white/80 dark:bg-gray-800/80 group-hover:scale-110 transition-transform duration-300">
                <card.icon className={`h-6 w-6 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent className="pt-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center text-sm">
                  {card.changeType === 'increase' ? (
                    <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
                  )}
                  <span className={`font-medium ${
                    card.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {card.change}
                  </span>
                  <span className="text-muted-foreground mr-1">من الشهر الماضي</span>
                </div>
              </div>
              <p className="text-xs text-muted-foreground mt-2">{card.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tabs for Different Views */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>نظرة عامة</span>
          </TabsTrigger>
          <TabsTrigger value="sales" className="flex items-center space-x-2">
            <DollarSign className="h-4 w-4" />
            <span>المبيعات</span>
          </TabsTrigger>
          <TabsTrigger value="products" className="flex items-center space-x-2">
            <Package className="h-4 w-4" />
            <span>المنتجات</span>
          </TabsTrigger>
          <TabsTrigger value="customers" className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>العملاء</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <EnhancedChart 
              title="أداء المبيعات الأسبوعية" 
              description="إحصائيات المبيعات لآخر 7 أيام"
            />
            <EnhancedChart 
              title="توزيع المنتجات" 
              description="أداء المنتجات حسب الفئة"
              type="bar"
            />
          </div>
        </TabsContent>

        <TabsContent value="sales" className="space-y-6">
          <EnhancedChart 
            title="تفاصيل المبيعات" 
            description="تحليل مفصل للمبيعات والإيرادات"
            type="line"
          />
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <EnhancedChart 
            title="أداء المنتجات" 
            description="المنتجات الأكثر مبيعاً والأقل أداءً"
            type="bar"
          />
        </TabsContent>

        <TabsContent value="customers" className="space-y-6">
          <EnhancedChart 
            title="سلوك العملاء" 
            description="تحليل سلوك العملاء وأنماط الشراء"
            type="area"
          />
        </TabsContent>
      </Tabs>

      {/* Report Generation Section */}
      <Card className="shadow-lg border-0">
        <CardHeader>
          <CardTitle className="text-xl">إنشاء التقارير</CardTitle>
          <CardDescription>قم بإنشاء تقارير مفصلة حسب الفترة الزمنية</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {reportTypes.map((report) => (
              <Card 
                key={report.type} 
                className="cursor-pointer hover:shadow-md transition-all duration-300 border-2 border-transparent hover:border-primary/20"
                onClick={() => onGenerateReport(report.type)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${report.color} text-white`}>
                      <report.icon className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-foreground">{report.title}</h3>
                      <p className="text-sm text-muted-foreground">{report.description}</p>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="w-full mt-3">
                    <Download className="h-3 w-3 mr-1" />
                    إنشاء التقرير
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
