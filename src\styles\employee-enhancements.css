/* Employee Interface Advanced Enhancements */

/* Advanced animations and transitions */
@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* Enhanced card animations */
.employee-card-animated {
  animation: fadeInUp 0.6s ease-out;
}

.employee-card-slide-right {
  animation: slideInFromRight 0.8s ease-out;
}

.employee-card-slide-left {
  animation: slideInFromLeft 0.8s ease-out;
}

/* Interactive elements */
.employee-interactive {
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.employee-interactive::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.employee-interactive:hover::before {
  left: 100%;
}

/* Advanced notification styles */
.employee-notification-urgent {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  animation: pulse 2s infinite;
}

.employee-notification-info {
  border-left: 4px solid #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.employee-notification-success {
  border-left: 4px solid #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.employee-notification-warning {
  border-left: 4px solid #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

/* Enhanced form elements */
.employee-form-group {
  position: relative;
  margin-bottom: 1.5rem;
}

.employee-form-label {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  font-size: 1rem;
  color: #6b7280;
  pointer-events: none;
  transition: all 0.2s ease-in-out;
  transform-origin: right top;
}

.employee-form-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.2s ease-in-out;
  background: transparent;
}

.employee-form-input:focus {
  outline: none;
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
}

.employee-form-input:focus + .employee-form-label,
.employee-form-input:not(:placeholder-shown) + .employee-form-label {
  transform: translateY(-1.5rem) scale(0.8);
  color: hsl(var(--primary));
  background: hsl(var(--background));
  padding: 0 0.25rem;
}

/* Advanced table enhancements */
.employee-table-enhanced {
  border-collapse: separate;
  border-spacing: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.employee-table-enhanced th {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  padding: 1rem;
  text-align: right;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
}

.employee-table-enhanced td {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease-in-out;
}

.employee-table-enhanced tr:hover td {
  background-color: #f8fafc;
}

.employee-table-enhanced tr:last-child td {
  border-bottom: none;
}

/* Status indicators */
.employee-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.employee-status-active {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
}

.employee-status-pending {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
}

.employee-status-inactive {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #991b1b;
}

/* Loading states */
.employee-loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .employee-table-enhanced {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }
  
  .employee-table-enhanced th {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    color: #f9fafb;
    border-bottom-color: #4b5563;
  }
  
  .employee-table-enhanced td {
    border-bottom-color: #374151;
  }
  
  .employee-table-enhanced tr:hover td {
    background-color: #374151;
  }
  
  .employee-notification-urgent {
    background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
  }
  
  .employee-notification-info {
    background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
  }
  
  .employee-notification-success {
    background: linear-gradient(135deg, #14532d 0%, #166534 100%);
  }
  
  .employee-notification-warning {
    background: linear-gradient(135deg, #92400e 0%, #b45309 100%);
  }
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .employee-card {
    margin: 0.5rem;
    padding: 1rem;
  }
  
  .employee-table-enhanced {
    font-size: 0.875rem;
  }
  
  .employee-table-enhanced th,
  .employee-table-enhanced td {
    padding: 0.5rem;
  }
}

/* Print styles */
@media print {
  .employee-card {
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
  
  .employee-button {
    display: none;
  }
  
  .employee-table-enhanced {
    box-shadow: none;
  }
}
