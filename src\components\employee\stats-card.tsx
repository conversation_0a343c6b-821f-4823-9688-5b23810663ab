'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: LucideIcon;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'danger';
  className?: string;
}

const variantStyles = {
  default: 'bg-white dark:bg-gray-800',
  primary: 'stats-card-primary',
  success: 'stats-card-success',
  warning: 'stats-card-warning',
  danger: 'stats-card-danger',
};

const iconStyles = {
  default: 'text-gray-600 dark:text-gray-400',
  primary: 'text-white',
  success: 'text-white',
  warning: 'text-white',
  danger: 'text-white',
};

export function StatsCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  variant = 'default',
  className,
}: StatsCardProps) {
  return (
    <Card className={cn(
      'employee-card employee-card-animated stats-card',
      variantStyles[variant],
      className
    )}>
      <CardHeader className="employee-card-header pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={cn(
              'p-3 rounded-xl',
              variant === 'default' 
                ? 'bg-primary/10' 
                : 'bg-white/20 backdrop-blur-sm'
            )}>
              <Icon className={cn(
                'h-6 w-6',
                variant === 'default' ? 'text-primary' : iconStyles[variant]
              )} />
            </div>
            <div>
              <CardTitle className={cn(
                'text-lg font-semibold',
                variant === 'default' ? 'text-foreground' : 'text-white'
              )}>
                {title}
              </CardTitle>
              {description && (
                <p className={cn(
                  'text-sm mt-1',
                  variant === 'default' ? 'text-muted-foreground' : 'text-white/80'
                )}>
                  {description}
                </p>
              )}
            </div>
          </div>
          {trend && (
            <div className={cn(
              'flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium',
              trend.isPositive 
                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300',
              variant !== 'default' && 'bg-white/20 text-white'
            )}>
              <span>{trend.isPositive ? '↗' : '↘'}</span>
              <span>{Math.abs(trend.value)}%</span>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="employee-card-content">
        <div className={cn(
          'text-3xl font-bold',
          variant === 'default' ? 'text-foreground' : 'text-white'
        )}>
          {value}
        </div>
        {trend && (
          <div className={cn(
            'text-sm mt-2',
            variant === 'default' ? 'text-muted-foreground' : 'text-white/80'
          )}>
            {trend.isPositive ? 'زيادة' : 'نقصان'} بنسبة {Math.abs(trend.value)}% عن الأمس
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Enhanced Stats Grid Component
interface StatsGridProps {
  stats: Array<Omit<StatsCardProps, 'className'>>;
  className?: string;
}

export function StatsGrid({ stats, className }: StatsGridProps) {
  return (
    <div className={cn(
      'grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
      className
    )}>
      {stats.map((stat, index) => (
        <StatsCard
          key={index}
          {...stat}
          className={cn(
            'employee-card-slide-right',
            `animation-delay-${index * 100}`
          )}
        />
      ))}
    </div>
  );
}

// Quick Action Card Component
interface QuickActionCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  onClick: () => void;
  badge?: string;
  variant?: 'default' | 'primary' | 'success' | 'warning';
  className?: string;
}

export function QuickActionCard({
  title,
  description,
  icon: Icon,
  onClick,
  badge,
  variant = 'default',
  className,
}: QuickActionCardProps) {
  return (
    <Card 
      className={cn(
        'employee-card employee-interactive cursor-pointer group',
        'hover:shadow-xl hover:scale-[1.02] transition-all duration-300',
        className
      )}
      onClick={onClick}
    >
      <CardContent className="employee-card-content">
        <div className="flex items-center gap-4">
          <div className={cn(
            'p-3 rounded-xl transition-colors group-hover:scale-110',
            variant === 'primary' && 'bg-primary/10 group-hover:bg-primary/20',
            variant === 'success' && 'bg-green-100 group-hover:bg-green-200 dark:bg-green-900 dark:group-hover:bg-green-800',
            variant === 'warning' && 'bg-yellow-100 group-hover:bg-yellow-200 dark:bg-yellow-900 dark:group-hover:bg-yellow-800',
            variant === 'default' && 'bg-gray-100 group-hover:bg-gray-200 dark:bg-gray-800 dark:group-hover:bg-gray-700'
          )}>
            <Icon className={cn(
              'h-6 w-6 transition-colors',
              variant === 'primary' && 'text-primary',
              variant === 'success' && 'text-green-600 dark:text-green-400',
              variant === 'warning' && 'text-yellow-600 dark:text-yellow-400',
              variant === 'default' && 'text-gray-600 dark:text-gray-400'
            )} />
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                {title}
              </h3>
              {badge && (
                <span className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
                  {badge}
                </span>
              )}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              {description}
            </p>
          </div>
          <div className="text-primary opacity-0 group-hover:opacity-100 transition-opacity">
            ←
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Performance Indicator Component
interface PerformanceIndicatorProps {
  label: string;
  current: number;
  target: number;
  unit?: string;
  className?: string;
}

export function PerformanceIndicator({
  label,
  current,
  target,
  unit = '',
  className,
}: PerformanceIndicatorProps) {
  const percentage = Math.min((current / target) * 100, 100);
  const isOnTrack = percentage >= 80;
  
  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-foreground">{label}</span>
        <span className="text-sm text-muted-foreground">
          {current}{unit} / {target}{unit}
        </span>
      </div>
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div 
          className={cn(
            'h-2 rounded-full transition-all duration-500',
            isOnTrack 
              ? 'bg-gradient-to-r from-green-500 to-green-400' 
              : 'bg-gradient-to-r from-yellow-500 to-orange-400'
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
      <div className="flex items-center justify-between text-xs">
        <span className={cn(
          'font-medium',
          isOnTrack ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'
        )}>
          {percentage.toFixed(1)}%
        </span>
        <span className="text-muted-foreground">
          {isOnTrack ? 'في المسار الصحيح' : 'يحتاج تحسين'}
        </span>
      </div>
    </div>
  );
}
