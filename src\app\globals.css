@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%; /* Light grey for backgrounds */
    --foreground: 207 10% 20%; /* Darker text for readability */

    --card: 0 0% 100%;
    --card-foreground: 207 10% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 207 10% 20%;

    /* Flutter-inspired Material Design Blue */
    --primary: 207 70% 49%; 
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 95%; /* Light grey for secondary elements */
    --secondary-foreground: 207 10% 30%;

    --muted: 0 0% 95%;
    --muted-foreground: 207 10% 45%;

    /* Flutter-inspired Material Design Teal for Accent */
    --accent: 174 100% 29%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 207 70% 49%; /* Matches new primary blue for focus rings */

    --radius: 0.5rem;

    /* Chart colors - can be adjusted as needed */
    --chart-1: 207 70% 49%; /* Primary blue */
    --chart-2: 174 100% 29%; /* Accent teal */
    --chart-3: 40 80% 60%; /* A complementary orange/yellow */
    --chart-4: 260 70% 65%; /* A complementary purple */
    --chart-5: 0 0% 50%; /* Neutral grey */

    /* Sidebar specific theme */
    --sidebar-background: 240 5.9% 10%; /* Darker sidebar for contrast */
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 207 70% 49%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 207 70% 49%; /* Blue for active/hover items in sidebar */
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 207 70% 49%;
  }

  .dark {
    --background: 207 10% 10%; /* Dark blue-grey background */
    --foreground: 0 0% 95%;

    --card: 207 10% 12%;
    --card-foreground: 0 0% 95%;

    --popover: 207 10% 10%;
    --popover-foreground: 0 0% 95%;

    /* Flutter-inspired Material Design Blue for Dark Mode */
    --primary: 207 70% 55%; 
    --primary-foreground: 0 0% 100%;

    --secondary: 207 10% 18%;
    --secondary-foreground: 0 0% 95%;

    --muted: 207 10% 18%;
    --muted-foreground: 0 0% 60%;

    /* Flutter-inspired Material Design Teal for Accent in Dark Mode */
    --accent: 174 100% 35%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 72.2% 50.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 207 10% 25%;
    --input: 207 10% 25%;
    --ring: 207 70% 55%; /* Matches new primary blue for dark mode focus rings */

    /* Chart colors - can be adjusted for dark mode */
    --chart-1: 207 70% 55%;
    --chart-2: 174 100% 35%;
    --chart-3: 40 70% 65%;
    --chart-4: 260 60% 70%;
    --chart-5: 0 0% 70%;

    /* Sidebar specific theme for dark mode */
    --sidebar-background: 207 10% 8%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 207 70% 55%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 207 70% 55%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 207 10% 15%;
    --sidebar-ring: 207 70% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans; /* Use a generic sans-serif stack */
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }
}

/* Ensuring Inter (or specified font) is used */
html {
  font-family: var(--font-inter), Arial, Helvetica, sans-serif;
}

/* Specific font for Arabic if Inter is not sufficient or a different aesthetic is desired */
html[dir="rtl"] {
  /* Example: Use a common Arabic font like Tajawal or Noto Sans Arabic if Inter is not ideal */
  /* font-family: 'Tajawal', var(--font-inter), Arial, Helvetica, sans-serif; */
}

/* Enhanced Login Form Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.login-form-container {
  animation: fadeInUp 0.6s ease-out;
}

.login-button:hover {
  animation: pulse 0.3s ease-in-out;
}

.shimmer-effect {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* Custom scrollbar for better UX */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}

/* Enhanced animations for owner interfaces */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Enhanced card hover effects */
.enhanced-card {
  animation: slideInFromRight 0.5s ease-out;
  background: linear-gradient(-45deg, #ffffff, #f8fafc, #ffffff, #f1f5f9);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
}

.dark .enhanced-card {
  background: linear-gradient(-45deg, #1e293b, #0f172a, #1e293b, #334155);
  background-size: 400% 400%;
}

/* Sidebar animations */
.sidebar-item {
  animation: slideInFromLeft 0.3s ease-out;
}

/* Stats card animations */
.stats-card {
  animation: scaleIn 0.4s ease-out;
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }
.stats-card:nth-child(5) { animation-delay: 0.5s; }

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced button hover effects */
.enhanced-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.enhanced-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.enhanced-button:hover::before {
  left: 100%;
}

/* Glass morphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
