
"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { BarChart3, DollarSign, ShoppingCart, TrendingUp, List, FileSignature } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import { Button } from '@/components/ui/button';
import { PlaceholderChart } from '@/components/dashboard/placeholder-chart';
import { EnhancedReportsDashboard } from '@/components/owner/enhanced-reports-dashboard';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from '@/hooks/use-toast'; // Import useToast

// Mock data for demonstration
const mockReportStats = {
  totalSales: "YER 150,750.00",
  totalOrders: 1230,
  averageOrderValue: "YER 122.56",
};

const mockBestSellingProducts = [
  { id: "prod001", name: "Organic Fuji Apples", sales: 250, revenue: "YER 197.50" },
  { id: "prod002", name: "Whole Grain Bread Loaf", sales: 180, revenue: "YER 628.20" },
  { id: "prod005", name: "Ground Coffee Beans (1kg)", sales: 150, revenue: "YER 2325.00" },
  { id: "prod003", name: "Free-Range Eggs (Dozen)", sales: 120, revenue: "YER 598.80" },
  { id: "prod006", name: "Cheddar Cheese Block", sales: 90, revenue: "YER 405.00" },
];

export default function OwnerReportsPage() {
  const t = useScopedI18n('ownerSalesReports');
  const tCommon = useScopedI18n('common');
  const { toast } = useToast(); // Initialize useToast

  const handleGenerateReport = (reportType: string) => {
    toast({
      title: t('reportGenerationInProgressTitle'),
      description: t('reportGenerationInProgressDesc', { reportType }),
    });
    // In a real app, this would trigger the actual report generation logic.
    // For now, it just shows a toast.
  };


  return (
    <AuthenticatedLayout expectedRole="owner">
      <div className="space-y-8 p-1">
        {/* Enhanced Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg">
              <BarChart3 className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                {t('title')}
              </h1>
              <p className="text-lg text-muted-foreground mt-1">
                {t('description')}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50">
              <TrendingUp className="h-3 w-3 mr-1" />
              تحديث مباشر
            </Badge>
          </div>
        </div>

        {/* Enhanced Reports Dashboard */}
        <EnhancedReportsDashboard
          stats={mockReportStats}
          t={t}
          onGenerateReport={handleGenerateReport}
        />

      </div>
    </AuthenticatedLayout>
  );
}
