"use client";

import React, { useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { useRouter } from 'next/navigation';
import { SiteHeader } from '@/components/layout/site-header';
import { SidebarNav } from '@/components/layout/sidebar-nav';
import { siteConfig } from '@/config/site';
import { Loader2 } from 'lucide-react';
import type { Role } from '@/types';

interface AuthenticatedLayoutProps {
  children: React.ReactNode;
  expectedRole: Role | Role[]; // Can be a single role string or an array of allowed roles
}

export function AuthenticatedLayout({ children, expectedRole }: AuthenticatedLayoutProps) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.replace('/'); // Redirect to login if not authenticated
    } else if (!isLoading && isAuthenticated && user) {
      const rolesToCheck = Array.isArray(expectedRole) ? expectedRole : [expectedRole];
      if (!rolesToCheck.includes(user.role)) {
        // If user's role is not in expected roles, redirect to their default dashboard or an error page.
        // For simplicity, redirecting to their role's dashboard.
        // A more robust solution would be an unauthorized page.
        router.replace(`/${user.role}/dashboard`);
        // Consider showing a toast message here for unauthorized access attempt.
      }
    }
  }, [user, isLoading, isAuthenticated, router, expectedRole]);

  if (isLoading || !user) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-16 w-16 animate-spin text-primary" />
      </div>
    );
  }
  
  const currentRoleConfig = siteConfig.roleNavs[user.role];
  if (!currentRoleConfig) {
     return (
      <div className="flex h-screen items-center justify-center">
        <p>Error: Role configuration not found.</p>
      </div>
    );
  }


  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950">
      <SiteHeader />
      <div className="container mx-auto flex-1 grid md:grid-cols-[260px_1fr] gap-8 lg:grid-cols-[300px_1fr] py-8">
        <aside className="hidden md:block w-full">
          <div className="sticky top-24 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 p-4">
            <SidebarNav items={currentRoleConfig.mainNav} />
          </div>
        </aside>
        <main className="flex w-full flex-col overflow-hidden">
          <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 p-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
