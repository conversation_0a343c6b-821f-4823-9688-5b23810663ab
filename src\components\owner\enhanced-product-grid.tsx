"use client";

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye,
  Package,
  DollarSign,
  Layers,
  Star,
  TrendingUp,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import Image from 'next/image';
import type { Product } from '@/types';

interface EnhancedProductGridProps {
  products: Product[];
  onEdit: (product: Product) => void;
  onDelete: (product: Product) => void;
  onView: (product: Product) => void;
  t: (key: string) => string;
  tCommon: (key: string) => string;
}

export function EnhancedProductGrid({ 
  products, 
  onEdit, 
  onDelete, 
  onView, 
  t, 
  tCommon 
}: EnhancedProductGridProps) {
  const getStockStatus = (quantity: number) => {
    if (quantity === 0) {
      return { 
        label: 'نفد المخزون', 
        variant: 'destructive' as const, 
        icon: AlertTriangle,
        color: 'text-red-600'
      };
    } else if (quantity <= 10) {
      return { 
        label: 'مخزون منخفض', 
        variant: 'secondary' as const, 
        icon: AlertTriangle,
        color: 'text-orange-600'
      };
    } else {
      return { 
        label: 'متوفر', 
        variant: 'default' as const, 
        icon: CheckCircle,
        color: 'text-green-600'
      };
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      'electronics': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      'clothing': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      'food': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      'books': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
      'home': 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200',
      'default': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    };
    return colors[category as keyof typeof colors] || colors.default;
  };

  if (products.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="mx-auto w-32 h-32 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900 dark:to-pink-900 rounded-full flex items-center justify-center mb-6">
          <Package className="h-16 w-16 text-purple-500" />
        </div>
        <h3 className="text-2xl font-bold text-foreground mb-3">لا توجد منتجات</h3>
        <p className="text-muted-foreground text-lg mb-6">ابدأ بإضافة منتجاتك الأولى</p>
        <Button className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700">
          <Package className="mr-2 h-4 w-4" />
          إضافة منتج جديد
        </Button>
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {products.map((product, index) => {
        const stockStatus = getStockStatus(product.quantity);
        const StockIcon = stockStatus.icon;
        
        return (
          <Card 
            key={product.id} 
            className="group hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 shadow-lg overflow-hidden"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            {/* Product Image */}
            <div className="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900">
              {product.imageUrl ? (
                <Image
                  src={product.imageUrl}
                  alt={product.name}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-300"
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <Package className="h-16 w-16 text-gray-400" />
                </div>
              )}
              
              {/* Overlay Actions */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => onView(product)}
                    className="bg-white/90 hover:bg-white text-gray-900"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => onEdit(product)}
                    className="bg-white/90 hover:bg-white text-gray-900"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Stock Status Badge */}
              <div className="absolute top-3 right-3">
                <Badge variant={stockStatus.variant} className="flex items-center space-x-1">
                  <StockIcon className="h-3 w-3" />
                  <span className="text-xs">{stockStatus.label}</span>
                </Badge>
              </div>

              {/* Category Badge */}
              <div className="absolute top-3 left-3">
                <Badge className={`text-xs ${getCategoryColor(product.category)}`}>
                  {product.category}
                </Badge>
              </div>
            </div>

            {/* Product Info */}
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg font-bold text-foreground line-clamp-2 group-hover:text-primary transition-colors">
                    {product.name}
                  </CardTitle>
                  {product.description && (
                    <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                      {product.description}
                    </p>
                  )}
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => onView(product)}>
                      <Eye className="mr-2 h-4 w-4" />
                      عرض التفاصيل
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onEdit(product)}>
                      <Edit className="mr-2 h-4 w-4" />
                      {tCommon('edit')}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => onDelete(product)}
                      className="text-red-600 focus:text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      {tCommon('delete')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>

            <CardContent className="pt-0">
              {/* Price and Stock Info */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-4 w-4 text-green-600" />
                    <span className="text-xl font-bold text-green-600">
                      {product.price.toLocaleString()} ر.ي
                    </span>
                  </div>
                  {product.rating && (
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="text-sm font-medium">{product.rating}</span>
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <Layers className="h-4 w-4 text-muted-foreground" />
                    <span className={stockStatus.color}>
                      {product.quantity} قطعة
                    </span>
                  </div>
                  {product.sales && (
                    <div className="flex items-center space-x-1 text-blue-600">
                      <TrendingUp className="h-3 w-3" />
                      <span className="text-xs">{product.sales} مبيعة</span>
                    </div>
                  )}
                </div>

                {/* Quick Actions */}
                <div className="flex space-x-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEdit(product)}
                    className="flex-1 text-xs"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    تعديل
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onView(product)}
                    className="flex-1 text-xs"
                  >
                    <Eye className="h-3 w-3 mr-1" />
                    عرض
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
