"use client";

import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Phone, 
  Mail,
  Shield,
  User,
  Calendar,
  Activity
} from "lucide-react";
import type { User } from '@/lib/auth';

interface EnhancedStaffTableProps {
  users: User[];
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
  t: (key: string) => string;
  tUserManagement: (key: string) => string;
  tCommon: (key: string) => string;
}

export function EnhancedStaffTable({ 
  users, 
  onEdit, 
  onDelete, 
  t, 
  tUserManagement, 
  tCommon 
}: EnhancedStaffTableProps) {
  const getInitials = (name?: string) => {
    if (!name) return "U";
    const parts = name.split(" ");
    if (parts.length > 1) {
      return `${parts[0][0]}${parts[parts.length - 1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'destructive';
      case 'owner':
        return 'default';
      case 'employee':
        return 'secondary';
      case 'customer':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
      case 'owner':
        return Shield;
      case 'employee':
        return User;
      case 'customer':
        return User;
      default:
        return User;
    }
  };

  if (users.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
          <User className="h-12 w-12 text-gray-400" />
        </div>
        <h3 className="text-lg font-semibold text-foreground mb-2">لا توجد موظفين</h3>
        <p className="text-muted-foreground">{t('noStaffMembers')}</p>
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="bg-gray-50 dark:bg-gray-800/50">
            <TableHead className="font-semibold">الموظف</TableHead>
            <TableHead className="font-semibold">{tUserManagement('phoneNumber')}</TableHead>
            <TableHead className="font-semibold">البريد الإلكتروني</TableHead>
            <TableHead className="font-semibold">{tUserManagement('role')}</TableHead>
            <TableHead className="font-semibold">تاريخ الانضمام</TableHead>
            <TableHead className="font-semibold">الحالة</TableHead>
            <TableHead className="text-center font-semibold">{tCommon('actions')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user, index) => {
            const RoleIcon = getRoleIcon(user.role);
            return (
              <TableRow 
                key={user.id} 
                className="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10 ring-2 ring-gray-200 dark:ring-gray-700">
                      <AvatarImage 
                        src={`https://i.pravatar.cc/40?u=${user.id}`} 
                        alt={user.name || user.username} 
                      />
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
                        {getInitials(user.name || user.username)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-semibold text-foreground">
                        {user.name || user.username}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        @{user.username}
                      </div>
                    </div>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="font-mono text-sm">{user.phoneNumber}</span>
                  </div>
                </TableCell>
                
                <TableCell>
                  {user.email ? (
                    <div className="flex items-center space-x-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{user.email}</span>
                    </div>
                  ) : (
                    <span className="text-muted-foreground text-sm">غير محدد</span>
                  )}
                </TableCell>
                
                <TableCell>
                  <Badge 
                    variant={getRoleBadgeVariant(user.role)} 
                    className="flex items-center space-x-1 w-fit"
                  >
                    <RoleIcon className="h-3 w-3" />
                    <span>{tUserManagement(user.role)}</span>
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      {user.createdAt ? new Date(user.createdAt).toLocaleDateString('ar-SA') : 'غير محدد'}
                    </span>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50">
                      <Activity className="h-3 w-3 mr-1" />
                      نشط
                    </Badge>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center justify-center">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          variant="ghost" 
                          className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
                        >
                          <span className="sr-only">فتح القائمة</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => onEdit(user)}
                          className="cursor-pointer"
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          {tCommon('edit')}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => onDelete(user)}
                          className="cursor-pointer text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          {tCommon('delete')}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
