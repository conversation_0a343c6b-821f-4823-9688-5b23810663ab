
"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { NotificationList } from '@/components/employee/notification-list';
import { Bell, CheckCircle2, AlertCircle, Info, Clock, Trash2, MarkAsUnread } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';

export default function EmployeeNotificationsPage() {
  const t = useScopedI18n('employeeNotifications');

  return (
    <AuthenticatedLayout expectedRole="employee">
      <div className="space-y-8 p-1">
        {/* Enhanced Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="p-3 bg-primary/10 rounded-2xl relative">
              <Bell className="h-8 w-8 text-primary" />
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-xs text-white font-bold">3</span>
              </div>
            </div>
            <div>
              <h1 className="text-4xl font-bold text-foreground">{t('title')}</h1>
              <p className="text-lg text-muted-foreground mt-1">
                {t('description')}
              </p>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex gap-3">
            <button className="flex items-center gap-2 px-4 py-2 bg-green-100 dark:bg-green-900 rounded-xl hover:bg-green-200 dark:hover:bg-green-800 transition-colors">
              <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
              <span className="text-sm font-medium text-green-700 dark:text-green-300">تحديد الكل كمقروء</span>
            </button>
            <button className="flex items-center gap-2 px-4 py-2 bg-red-100 dark:bg-red-900 rounded-xl hover:bg-red-200 dark:hover:bg-red-800 transition-colors">
              <Trash2 className="h-4 w-4 text-red-600 dark:text-red-400" />
              <span className="text-sm font-medium text-red-700 dark:text-red-300">حذف المقروءة</span>
            </button>
          </div>
        </div>

        <NotificationList />
      </div>
    </AuthenticatedLayout>
  );
}
